{"name": "haunt-2022", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "node --no-deprecation index.js", "dev": "node index.js", "test": "echo \"Error: no test specified\" && exit 1", "pm2:start": "pm2 start ecosystem.config.js --env production", "pm2:stop": "pm2 stop adore-bot", "pm2:restart": "pm2 restart adore-bot", "pm2:reload": "pm2 reload adore-bot", "pm2:delete": "pm2 delete adore-bot", "pm2:logs": "pm2 logs adore-bot", "pm2:status": "pm2 status", "deploy": "pm2 deploy production"}, "author": "", "license": "ISC", "dependencies": {"axios": "^1.9.0", "chalk": "^4.1.2", "color": "^5.0.0", "color-name": "^2.0.0", "comma-number": "^2.1.0", "common-tags": "^1.8.2", "discord.js": "^14.14.1", "dotenv": "^16.5.0", "glob": "^8.0.3", "got": "^14.4.2", "js-base64": "^3.7.2", "luxon": "^3.6.1", "moment": "^2.29.4", "mongoose": "^6.6.0", "ms": "^2.1.3", "node-fetch": "^2.6.7", "node-location-timezone": "^1.1.0", "relevant-urban": "^2.0.0", "superagent": "^8.0.0", "urban": "^0.3.4", "yt-search": "^2.10.3"}, "devDependencies": {"pm2": "^5.3.0"}}