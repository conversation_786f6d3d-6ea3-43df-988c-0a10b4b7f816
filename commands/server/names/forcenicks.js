const { EmbedBuilder, PermissionFlagsBits } = require('discord.js');
const { embeds } = require('../../../core/embeds');
const { hasDiscordPermission } = require('../../../utils/permissions');
const { Forcenick } = require('../../../database');
const { createPagination } = require('../../../core/buttons');
const config = require('../../../config/setup');

module.exports = {
  name: "forcenicks",
  aliases: ['fnlist', 'forcednames'],
  description: `list all forced nicknames in the server`,
  usage: '{guildprefix}forcenicks',
  permission: "Manage Nicknames",
  run: async(client, message, args) => {

    if (!hasDiscordPermission(message, PermissionFlagsBits.ManageNicknames, 'Manage Nicknames')) return;

    try {
      const forcedNicks = await Forcenick.find({ GuildID: message.guild.id });

      if (forcedNicks.length === 0) {
        return embeds.warn(message, 'No forced nicknames found in this server');
      }

      // Filter out users who are no longer in the server and get valid data
      const validForcedNicks = [];
      for (const forcedNick of forcedNicks) {
        const member = message.guild.members.cache.get(forcedNick.UserID);
        if (member) {
          validForcedNicks.push({
            ...forcedNick.toObject(),
            member: member
          });
        } else {
          // Clean up database - remove forced nicknames for users no longer in server
          await Forcenick.findOneAndDelete({
            GuildID: message.guild.id,
            UserID: forcedNick.UserID
          });
        }
      }

      if (validForcedNicks.length === 0) {
        return embeds.warn(message, 'No forced nicknames found for current server members');
      }

      if (validForcedNicks.length <= 10) {
        // Show simple embed for small lists
        const forcedNickList = validForcedNicks.map((forcedNick, index) => {
          const number = (index + 1).toString().padStart(2, '0');
          const setBy = message.guild.members.cache.get(forcedNick.SetBy);
          const setByText = setBy ? setBy.user.tag : 'Unknown User';
          
          return `\`${number}\` **${forcedNick.member.user.tag}** → **${forcedNick.ForcedNickname}**\n    Set by: ${setByText}`;
        }).join('\n\n');

        const embed = new EmbedBuilder()
          .setColor(config.colors.embed)
          .setTitle('Forced Nicknames')
          .setDescription(forcedNickList)
          .setFooter({ text: `${validForcedNicks.length} forced nicknames` });

        return message.channel.send({ embeds: [embed] });

      } else {
        // Use pagination for larger lists
        const formatPage = (pageForcedNicks, currentPage, totalPages) => {
          const startIndex = (currentPage - 1) * 10;

          const forcedNickList = pageForcedNicks.map((forcedNick, index) => {
            const number = (startIndex + index + 1).toString().padStart(2, '0');
            const setBy = message.guild.members.cache.get(forcedNick.SetBy);
            const setByText = setBy ? setBy.user.tag : 'Unknown User';
            
            return `\`${number}\` **${forcedNick.member.user.tag}** → **${forcedNick.ForcedNickname}**\n    Set by: ${setByText}`;
          }).join('\n\n');

          return new EmbedBuilder()
            .setColor(config.colors.embed)
            .setTitle('Forced Nicknames')
            .setDescription(forcedNickList)
            .setFooter({ text: `${validForcedNicks.length} forced nicknames • Page ${currentPage}/${totalPages}` });
        };

        await createPagination(message, validForcedNicks, formatPage, 10, 'Forced Nicknames');
      }

    } catch (error) {
      embeds.warn(message, 'An error occurred while fetching forced nicknames.');
    }
  }
}
