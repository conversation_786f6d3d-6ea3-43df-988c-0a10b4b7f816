const { PermissionFlagsBits } = require("discord.js");
const { embeds } = require('../../../core/embeds');
const { hasDiscordPermission, checkSelfAction, checkBotAction, checkUserMemberHierarchy, checkBotMemberHierarchy } = require('../../../utils/permissions');
const { runHelpCommand } = require('../../../utils/commandProcessor');
const { Forcenick } = require('../../../database');

module.exports = {
  name: "forcenick",
  aliases: ['fn'],
  description: `force a nickname on a user that will be automatically restored if they try to change it`,
  usage: '{guildprefix}forcenick [user] [nickname] - force a nickname\n{guildprefix}forcenick [user] - remove forced nickname',
  permission: "Manage Nicknames",
  run: async(client, message, args) => {

    if (!hasDiscordPermission(message, PermissionFlagsBits.ManageNicknames, 'Manage Nicknames')) return;

    if (!message.guild.members.me.permissions.has(PermissionFlagsBits.ManageNicknames)) {
      return embeds.deny(message, 'I need **Manage Nicknames** permission to use this command');
    }

    // Check if user provided input
    if (!args[0]) {
      return runHelpCommand(message, 'forcenick');
    }

    // Use universal user finder with error handling
    let userResult;
    try {
      userResult = client.findUser(message.guild, args[0]);
    } catch (error) {
      return embeds.warn(message, 'An error occurred while searching for the user. Please try again.');
    }

    // Check if userResult is valid and has the expected structure
    if (!userResult || typeof userResult !== 'object') {
      return embeds.warn(message, 'An error occurred while searching for the user. Please try again.');
    }

    if (!userResult.found) {
      return embeds.warn(message, userResult.error || 'User not found');
    }

    const user = userResult.user;
    const nickname = args.slice(1).join(" ");

    if (!checkSelfAction(message, user, 'forcenick')) return;
    if (!checkBotAction(message, user)) return;

    // Hierarchy checks
    if (!checkUserMemberHierarchy(message, user)) return;
    if (!checkBotMemberHierarchy(message, user)) return;

    try {
      if (!nickname) {
        // Remove forced nickname
        const existingForcenick = await Forcenick.findOne({
          GuildID: message.guild.id,
          UserID: user.user.id
        });

        if (!existingForcenick) {
          return embeds.warn(message, `**${user.user.tag}** doesn't have a forced nickname`);
        }

        await Forcenick.findOneAndDelete({
          GuildID: message.guild.id,
          UserID: user.user.id
        });

        return embeds.success(message, `Removed forced nickname for **${user.user.tag}**`);

      } else {
        // Set forced nickname
        if (nickname.length > 32) {
          return embeds.warn(message, 'Nickname cannot be longer than 32 characters');
        }

        // Set the nickname first
        await user.setNickname(nickname, `Forced nickname by ${message.author.tag}`);

        // Save to database (upsert - update if exists, create if not)
        await Forcenick.findOneAndUpdate(
          {
            GuildID: message.guild.id,
            UserID: user.user.id
          },
          {
            GuildID: message.guild.id,
            UserID: user.user.id,
            ForcedNickname: nickname,
            SetBy: message.author.id,
            SetAt: new Date()
          },
          {
            upsert: true,
            new: true
          }
        );

        return embeds.success(message, `**${user.user.tag}** now has a forced nickname: **${nickname}**`);
      }

    } catch (error) {
      embeds.warn(message, 'An error occurred while setting the forced nickname.');
    }
  }
}
