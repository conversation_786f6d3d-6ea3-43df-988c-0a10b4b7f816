const { PermissionFlagsBits, EmbedBuilder } = require("discord.js");
const { embeds } = require('../../../core/embeds');
const { hasDiscordPermission, checkSelfAction, checkBotAction, checkUserMemberHierarchy, checkBotMemberHierarchy } = require('../../../utils/permissions');
const { runHelpCommand } = require('../../../utils/commandProcessor');
const { Forcenick } = require('../../../database');
const { createPagination, createConfirmation } = require('../../../core/buttons');
const config = require('../../../config/setup');
const { findUser } = require('../../../handlers/finder');

module.exports = {
  name: "forcenick",
  aliases: ['fn'],
  description: `force a nickname on a user that will be automatically restored if they try to change it`,
  usage: '{guildprefix}forcenick [user] [nickname] - force a nickname\n{guildprefix}forcenick [user] - remove forced nickname\n{guildprefix}forcenick list - show all forced nicknames\n{guildprefix}forcenick reset - remove all forced nicknames',
  permission: "Manage Nicknames",
  run: async(client, message, args) => {

    if (!hasDiscordPermission(message, PermissionFlagsBits.ManageNicknames, 'Manage Nicknames')) return;

    if (!message.guild.members.me.permissions.has(PermissionFlagsBits.ManageNicknames)) {
      return embeds.deny(message, 'I need **Manage Nicknames** permission to use this command');
    }

    // Check if user provided input
    if (!args[0]) {
      return runHelpCommand(message, 'forcenick');
    }

    // Handle list subcommand
    if (args[0] === 'list') {
      try {
        const forcedNicks = await Forcenick.find({ GuildID: message.guild.id });

        if (forcedNicks.length === 0) {
          return embeds.warn(message, 'No forced nicknames found in this server');
        }

        // Filter out users who are no longer in the server and get valid data
        const validForcedNicks = [];
        for (const forcedNick of forcedNicks) {
          const member = message.guild.members.cache.get(forcedNick.UserID);
          if (member) {
            validForcedNicks.push({
              ...forcedNick.toObject(),
              member: member
            });
          } else {
            // Clean up database - remove forced nicknames for users no longer in server
            await Forcenick.findOneAndDelete({
              GuildID: message.guild.id,
              UserID: forcedNick.UserID
            });
          }
        }

        if (validForcedNicks.length === 0) {
          return embeds.warn(message, 'No forced nicknames found for current server members');
        }

        if (validForcedNicks.length <= 10) {
          // Show simple embed for small lists
          const forcedNickList = validForcedNicks.map((forcedNick, index) => {
            const number = (index + 1).toString().padStart(2, '0');
            const setBy = message.guild.members.cache.get(forcedNick.SetBy);
            const setByText = setBy ? setBy.user.tag : 'Unknown User';

            return `\`${number}\` **${forcedNick.member.user.tag}** → **${forcedNick.ForcedNickname}**\n    Set by: ${setByText}`;
          }).join('\n\n');

          const embed = new EmbedBuilder()
            .setColor(config.colors.embed)
            .setTitle('Forced Nicknames')
            .setDescription(forcedNickList)
            .setFooter({ text: `${validForcedNicks.length} forced nicknames` });

          return message.channel.send({ embeds: [embed] });

        } else {
          // Use pagination for larger lists
          const formatPage = (pageForcedNicks, currentPage, totalPages) => {
            const startIndex = (currentPage - 1) * 10;

            const forcedNickList = pageForcedNicks.map((forcedNick, index) => {
              const number = (startIndex + index + 1).toString().padStart(2, '0');
              const setBy = message.guild.members.cache.get(forcedNick.SetBy);
              const setByText = setBy ? setBy.user.tag : 'Unknown User';

              return `\`${number}\` **${forcedNick.member.user.tag}** → **${forcedNick.ForcedNickname}**\n    Set by: ${setByText}`;
            }).join('\n\n');

            return new EmbedBuilder()
              .setColor(config.colors.embed)
              .setTitle('Forced Nicknames')
              .setDescription(forcedNickList)
              .setFooter({ text: `${validForcedNicks.length} forced nicknames • Page ${currentPage}/${totalPages}` });
          };

          await createPagination(message, validForcedNicks, formatPage, 10, 'Forced Nicknames');
        }

      } catch (error) {
        return embeds.warn(message, 'An error occurred while fetching forced nicknames.');
      }
      return;
    }

    // Handle reset subcommand
    if (args[0] === 'reset') {
      try {
        const forcedNicks = await Forcenick.find({ GuildID: message.guild.id });

        if (forcedNicks.length === 0) {
          return embeds.warn(message, 'No forced nicknames found to reset');
        }

        // Show confirmation dialog
        await createConfirmation(
          message,
          `Are you sure you want to **remove all ${forcedNicks.length} forced nicknames**?`,
          async (interaction) => {
            // On approve: delete all forced nicknames
            await Forcenick.deleteMany({ GuildID: message.guild.id });

            // Edit the confirmation embed to show success message
            const successEmbed = new EmbedBuilder()
              .setColor(config.colors.success)
              .setDescription(`✅ <@${message.author.id}>: Removed all forced nicknames`);

            await interaction.editReply({ embeds: [successEmbed], components: [] });
          }
        );

      } catch (error) {
        return embeds.warn(message, 'An error occurred while resetting forced nicknames.');
      }
      return;
    }

    // Use universal user finder
    const userResult = await findUser(message.guild, args[0], client);

    if (!userResult.found) {
      return embeds.warn(message, userResult.error || 'User not found');
    }

    // For forcenick, we need a guild member, not a global user
    if (userResult.isGlobal) {
      return embeds.warn(message, 'User is not in this server');
    }

    const member = userResult.user;
    const nickname = args.slice(1).join(" ");

    if (!checkSelfAction(message, member, 'forcenick')) return;
    if (!checkBotAction(message, member)) return;

    // Hierarchy checks
    if (!checkUserMemberHierarchy(message, member)) return;
    if (!checkBotMemberHierarchy(message, member)) return;

    try {
      if (!nickname) {
        // Remove forced nickname
        const existingForcenick = await Forcenick.findOne({
          GuildID: message.guild.id,
          UserID: member.user.id
        });

        if (!existingForcenick) {
          return embeds.warn(message, `**${member.user.tag}** doesn't have a forced nickname`);
        }

        await Forcenick.findOneAndDelete({
          GuildID: message.guild.id,
          UserID: member.user.id
        });

        return embeds.success(message, `Removed forced nickname for **${member.user.tag}**`);

      } else {
        // Set forced nickname
        if (nickname.length > 32) {
          return embeds.warn(message, 'Nickname cannot be longer than 32 characters');
        }

        // Set the nickname first
        await member.setNickname(nickname, `Forced nickname by ${message.author.tag}`);

        // Save to database (upsert - update if exists, create if not)
        await Forcenick.findOneAndUpdate(
          {
            GuildID: message.guild.id,
            UserID: member.user.id
          },
          {
            GuildID: message.guild.id,
            UserID: member.user.id,
            ForcedNickname: nickname,
            SetBy: message.author.id,
            SetAt: new Date()
          },
          {
            upsert: true,
            new: true
          }
        );

        return embeds.success(message, `**${member.user.tag}** now has a forced nickname: **${nickname}**`);
      }

    } catch (error) {
      embeds.warn(message, 'An error occurred while setting the forced nickname.');
    }
  }
}
