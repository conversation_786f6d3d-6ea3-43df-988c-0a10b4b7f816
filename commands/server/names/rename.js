const { PermissionFlagsBits } = require("discord.js");
const { embeds } = require('../../../core/embeds');
const { hasDiscordPermission, checkSelfAction, checkBotAction, checkUserMemberHierarchy, checkBotMemberHierarchy } = require('../../../utils/permissions');
const { runHelpCommand } = require('../../../utils/commandProcessor');

module.exports = {
  name: "rename",
  aliases: ['nick'],
  description: `change a member's nickname`,
  usage: '{guildprefix}rename [user]\n{guildprefix}rename [user] [nickname]',
  permission: "Manage Nicknames",
  run: async(client, message, args) => {

    if (!hasDiscordPermission(message, PermissionFlagsBits.ManageNicknames, 'Manage Nicknames')) return;

    if (!message.guild.members.me.permissions.has(PermissionFlagsBits.ManageNicknames)) {
      return embeds.deny(message, 'I need **Manage Nicknames** permission to use this command');
    }

    // Check if user provided input
    if (!args[0]) {
      return runHelpCommand(message, 'rename');
    }

    // Use universal user finder with error handling
    let userResult;
    try {
      userResult = client.findUser(message.guild, args[0]);
    } catch (error) {
      return embeds.warn(message, 'An error occurred while searching for the user. Please try again.');
    }

    // Check if userResult is valid and has the expected structure
    if (!userResult || typeof userResult !== 'object') {
      return embeds.warn(message, 'An error occurred while searching for the user. Please try again.');
    }

    if (!userResult.found) {
      return embeds.warn(message, userResult.error || 'User not found');
    }

    const member = userResult.user;

    let nickname = args.slice(1).join(" ");

    if (!nickname) {
      nickname = null;
    }

    if (!checkSelfAction(message, member, 'rename')) return;
    if (!checkBotAction(message, member)) return;

    // Hierarchy checks
    if (!checkUserMemberHierarchy(message, member)) return;
    if (!checkBotMemberHierarchy(message, member)) return;

    try {
      await member.setNickname(nickname, `Renamed by ${message.author.tag}`);

      if (nickname) {
        embeds.success(message, `**${member.user.tag}** has been renamed to **${nickname}**`);
      } else {
        embeds.success(message, `**${member.user.tag}**'s nickname has been reset`);
      }

    } catch (error) {
      embeds.warn(message, 'An error occurred while trying to rename this user.');
    }
  }
}
