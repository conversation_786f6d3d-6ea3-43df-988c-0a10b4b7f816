const { PermissionFlagsBits, ChannelType } = require('discord.js');
const { embeds } = require('../../../core/embeds');
const { hasDiscordPermission, checkSelfAction, checkBotAction } = require('../../../utils/permissions');
const { runHelpCommand } = require('../../../utils/commandProcessor');

module.exports = {
  name: "pic",
  description: "give user pic permissions (ability to send images/embeds)",
  usage: '{guildprefix}pic [user]',
  permission: "Manage Roles",
  run: async(client, message, args) => {

    if (!hasDiscordPermission(message, PermissionFlagsBits.ManageRoles, 'Manage Roles')) return;

    if (!message.guild.members.me.permissions.has(PermissionFlagsBits.ManageRoles)) {
      return embeds.deny(message, 'I need **Manage Roles** permission to use this command');
    }

    if (!args[0]) {
      return runHelpCommand(message, 'pic');
    }

    // Use universal user finder
    const userResult = client.findUser(message.guild, args[0]);

    if (!userResult.found) {
      return embeds.warn(message, userResult.error || 'User not found');
    }

    const targetMember = userResult.user;

    if (!checkSelfAction(message, targetMember, 'give pic permissions to')) return;
    if (!checkBotAction(message, targetMember)) return;

    try {
      // Find or create the pic role
      let picRole = message.guild.roles.cache.find(role => role.name.toLowerCase() === 'pic');

      if (!picRole) {
        try {
          const statusMessage = await embeds.warn(message, 'No **pic** role found, creating one...');

          picRole = await message.guild.roles.create({
            name: 'pic',
            permissions: [],
            reason: `Pic role created by ${message.author.tag}`
          });

          // Set permissions for text channels
          const textChannels = message.guild.channels.cache.filter(c => c.type === ChannelType.GuildText);
          for (const [, channel] of textChannels) {
            try {
              await channel.permissionOverwrites.create(picRole, {
                AttachFiles: true,
                EmbedLinks: true
              });
            } catch (permError) {
              // Skip channels where we can't set permissions
            }
          }

          // Remove permissions for voice channels
          const voiceChannels = message.guild.channels.cache.filter(c => c.type === ChannelType.GuildVoice);
          for (const [, channel] of voiceChannels) {
            try {
              await channel.permissionOverwrites.create(picRole, {
                ViewChannel: false,
                Connect: false
              });
            } catch (permError) {
              // Skip channels where we can't set permissions
            }
          }

          // Edit the status message to show success
          setTimeout(async () => {
            try {
              await statusMessage.edit({ embeds: [{ 
                color: parseInt(require('../../../config/setup').colors.success.replace('#', ''), 16),
                description: `✅ <@${message.author.id}>: The **pic** role has been created`
              }] });
            } catch (editError) {
              // Message might have been deleted
            }
          }, 2000);

        } catch (error) {
          return embeds.warn(message, 'Failed to create the **pic** role. Please check my permissions.');
        }
      }

      // Check if user already has the role
      if (targetMember.roles.cache.has(picRole.id)) {
        return embeds.warn(message, `**${targetMember.user.tag}** already has pic permissions`);
      }

      // Add the role
      await targetMember.roles.add(picRole, `Pic permissions given by ${message.author.tag}`);

      return embeds.success(message, `**${targetMember.user.tag}** has been given pic permissions`);

    } catch (error) {
      return embeds.warn(message, 'An error occurred while giving pic permissions.');
    }
  }
}
