const { EmbedBuilder } = require('discord.js');
const { embeds } = require('../../../core/embeds');
const { colors } = require('../../../config/setup');

module.exports = {
  name: "banner",
  aliases: ['userbanner'],
  description: "show a user's global banner",
  usage: '{guildprefix}banner [user]',
  run: async(client, message, args) => {

    try {
      let targetUser = message.author;

      // If user provided an argument, find the user
      if (args[0]) {
        const userResult = client.findUser(message.guild, args[0]);

        if (userResult.found) {
          // Get the user object from the member
          targetUser = userResult.user.user || userResult.user;
          // If it's a member object, get the user
          if (targetUser.user) {
            targetUser = targetUser.user;
          }
        } else {
          return embeds.warn(message, userResult.error || 'User not found');
        }
      }

      // Fetch the full user object to get banner info
      const fullUser = await client.users.fetch(targetUser.id, { force: true });
      
      const bannerURL = fullUser.bannerURL({ 
        dynamic: true, 
        size: 4096 
      });

      if (!bannerURL) {
        return embeds.warn(message, `**${fullUser.username}** doesn't have a banner set`);
      }

      const embed = new EmbedBuilder()
        .setColor(colors.embed)
        .setTitle(`${fullUser.username}'s Banner`)
        .setImage(bannerURL)
        .setFooter({ 
          text: `Requested by ${message.author.username}`, 
          iconURL: message.author.displayAvatarURL({ dynamic: true }) 
        });

      return message.channel.send({ embeds: [embed] });

    } catch (error) {
      return embeds.warn(message, 'An error occurred while fetching the banner.');
    }
  }
}
