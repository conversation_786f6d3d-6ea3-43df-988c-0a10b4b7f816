const { EmbedBuilder } = require('discord.js');
const { embeds } = require('../../../core/embeds');
const { colors } = require('../../../config/setup');

module.exports = {
  name: "guildsplash",
  aliases: ['serversplash', 'splash'],
  description: "show the server's invite splash screen",
  usage: '{guildprefix}guildsplash',
  run: async(client, message, args) => {

    try {
      const splashURL = message.guild.splashURL({ 
        dynamic: true, 
        size: 4096 
      });

      if (!splashURL) {
        return embeds.warn(message, `**${message.guild.name}** doesn't have a splash screen set`);
      }

      const embed = new EmbedBuilder()
        .setColor(colors.embed)
        .setTitle(`${message.guild.name}'s Splash Screen`)
        .setImage(splashURL)
        .setFooter({ 
          text: `Requested by ${message.author.username}`, 
          iconURL: message.author.displayAvatarURL({ dynamic: true }) 
        });

      return message.channel.send({ embeds: [embed] });

    } catch (error) {
      return embeds.warn(message, 'An error occurred while fetching the server splash screen.');
    }
  }
}
