const { EmbedBuilder } = require('discord.js');
const { embeds } = require('../../../core/embeds');
const { colors } = require('../../../config/setup');

module.exports = {
  name: "guildicon",
  aliases: ['servericon', 'icon'],
  description: "show the server's icon",
  usage: '{guildprefix}guildicon',
  run: async(client, message, args) => {

    try {
      const iconURL = message.guild.iconURL({ 
        dynamic: true, 
        size: 4096 
      });

      if (!iconURL) {
        return embeds.warn(message, `**${message.guild.name}** doesn't have an icon set`);
      }

      const embed = new EmbedBuilder()
        .setColor(colors.embed)
        .setTitle(`${message.guild.name}'s Icon`)
        .setImage(iconURL)
        .setFooter({ 
          text: `Requested by ${message.author.username}`, 
          iconURL: message.author.displayAvatarURL({ dynamic: true }) 
        });

      return message.channel.send({ embeds: [embed] });

    } catch (error) {
      return embeds.warn(message, 'An error occurred while fetching the server icon.');
    }
  }
}
