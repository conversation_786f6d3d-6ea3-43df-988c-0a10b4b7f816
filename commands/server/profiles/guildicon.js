const { EmbedBuilder } = require('discord.js');
const { embeds } = require('../../../core/embeds');
const { colors } = require('../../../config/setup');

module.exports = {
  name: "guildicon",
  aliases: ['servericon', 'icon'],
  description: "show a server's icon",
  usage: '{guildprefix}guildicon [server id]',
  run: async(client, message, args) => {

    try {
      let targetGuild = message.guild;

      // If server ID provided, try to fetch that guild
      if (args[0]) {
        if (!/^\d{17,19}$/.test(args[0])) {
          return embeds.warn(message, 'Please provide a valid server ID');
        }

        try {
          targetGuild = await client.guilds.fetch(args[0]);
        } catch (error) {
          return embeds.warn(message, 'Server not found or bot is not in that server');
        }
      }

      const iconURL = targetGuild.iconURL({
        dynamic: true,
        size: 4096
      });

      if (!iconURL) {
        return embeds.warn(message, `**${targetGuild.name}** doesn't have an icon set`);
      }

      const embed = new EmbedBuilder()
        .setColor(colors.embed)
        .setTitle(`${targetGuild.name}'s Icon`)
        .setImage(iconURL)
        .setFooter({
          text: `Requested by ${message.author.username}`,
          iconURL: message.author.displayAvatarURL({ dynamic: true })
        });

      return message.channel.send({ embeds: [embed] });

    } catch (error) {
      return embeds.warn(message, 'An error occurred while fetching the server icon.');
    }
  }
}
