const { EmbedBuilder } = require('discord.js');
const { embeds } = require('../../../core/embeds');
const { colors } = require('../../../config/setup');

module.exports = {
  name: "guildbanner",
  aliases: ['serverbanner2', 'gbanner'],
  description: "show the server's banner",
  usage: '{guildprefix}guildbanner',
  run: async(client, message, args) => {

    try {
      const bannerURL = message.guild.bannerURL({ 
        dynamic: true, 
        size: 4096 
      });

      if (!bannerURL) {
        return embeds.warn(message, `**${message.guild.name}** doesn't have a banner set`);
      }

      const embed = new EmbedBuilder()
        .setColor(colors.embed)
        .setTitle(`${message.guild.name}'s Banner`)
        .setImage(bannerURL)
        .setFooter({ 
          text: `Requested by ${message.author.username}`, 
          iconURL: message.author.displayAvatarURL({ dynamic: true }) 
        });

      return message.channel.send({ embeds: [embed] });

    } catch (error) {
      return embeds.warn(message, 'An error occurred while fetching the server banner.');
    }
  }
}
