const { EmbedBuilder } = require('discord.js');
const { embeds } = require('../../../core/embeds');
const { colors } = require('../../../config/setup');

module.exports = {
  name: "avatar",
  aliases: ['av', 'pfp'],
  description: "show a user's global avatar",
  usage: '{guildprefix}avatar [user]',
  run: async(client, message, args) => {

    try {
      let targetUser = message.author;

      // If user provided an argument, find the user
      if (args[0]) {
        const userResult = client.findUser(message.guild, args[0]);
        
        if (userResult.found) {
          targetUser = userResult.user.user || userResult.user;
        } else {
          return embeds.warn(message, userResult.error || 'User not found');
        }
      }

      // Get the user's global avatar
      const avatarURL = targetUser.displayAvatarURL({ 
        dynamic: true, 
        size: 4096 
      });

      const embed = new EmbedBuilder()
        .setColor(colors.embed)
        .setTitle(`${targetUser.username}'s Avatar`)
        .setImage(avatarURL)
        .setFooter({ 
          text: `Requested by ${message.author.username}`, 
          iconURL: message.author.displayAvatarURL({ dynamic: true }) 
        });

      return message.channel.send({ embeds: [embed] });

    } catch (error) {
      return embeds.warn(message, 'An error occurred while fetching the avatar.');
    }
  }
}
