const { EmbedBuilder } = require('discord.js');
const { embeds } = require('../../../core/embeds');
const { colors } = require('../../../config/setup');

module.exports = {
  name: "savatar",
  aliases: ['serveravatar', 'sav'],
  description: "show a user's server-specific avatar",
  usage: '{guildprefix}savatar [user]',
  run: async(client, message, args) => {

    try {
      let targetMember = message.member;

      // If user provided an argument, find the member
      if (args[0]) {
        const userResult = client.findUser(message.guild, args[0]);

        if (userResult.found) {
          // Ensure we have a member object, not just a user
          targetMember = userResult.user;
          // If it's just a user object, try to get the member
          if (!targetMember.guild) {
            targetMember = message.guild.members.cache.get(targetMember.id) || targetMember;
          }
        } else {
          return embeds.warn(message, userResult.error || 'User not found');
        }
      }

      // Get the member's server avatar (falls back to global avatar if no server avatar)
      const avatarURL = targetMember.displayAvatarURL({ 
        dynamic: true, 
        size: 4096 
      });

      // Check if they have a server-specific avatar
      const hasServerAvatar = targetMember.avatar !== null;
      const avatarType = hasServerAvatar ? 'Server Avatar' : 'Global Avatar (No Server Avatar Set)';

      const embed = new EmbedBuilder()
        .setColor(colors.embed)
        .setTitle(`${targetMember.user.username}'s ${avatarType}`)
        .setImage(avatarURL)
        .setFooter({ 
          text: `Requested by ${message.author.username}`, 
          iconURL: message.author.displayAvatarURL({ dynamic: true }) 
        });

      return message.channel.send({ embeds: [embed] });

    } catch (error) {
      return embeds.warn(message, 'An error occurred while fetching the server avatar.');
    }
  }
}
