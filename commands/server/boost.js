const { EmbedBuilder } = require('discord.js');
const { embeds } = require('../../core/embeds');
const { ensureGuildData } = require('../../database/global');
const { EmbedVariableProcessor } = require('../../utils/embedbuilder');
const config = require('../../config/setup');

module.exports = {
  name: "boost",
  aliases: ['boosts'],
  description: `Set up a boost message for when members boost the server`,
  usage: '{guildprefix}boost add [channel] [message]\n{guildprefix}boost remove [channel]\n{guildprefix}boost view [channel]\n{guildprefix}boost check\n\n{guildprefix}boost variables',
  permission: "Administrator",

  run: async(client, message, args) => {

    // Show help if no subcommand provided
    if (!args[0]) return false;

    const globaldata = await ensureGuildData(message.guild.id);

    if (args[0] === 'add') {

      const channelInput = args[1];
      const boostmessage = args.slice(2).join(' ');

      if (!channelInput || !boostmessage) return false;

      // Use universal finder for channel detection
      const channelResult = message.mentions.channels.first() ?
        { found: true, channel: message.mentions.channels.first() } :
        client.findChannel(message.guild, channelInput);

      if (!channelResult.found) return false;

      const boostchannel = channelResult.channel;

      // Process the message using EmbedVariableProcessor for embed/text detection
      try {
        EmbedVariableProcessor.process(message.channel, boostmessage, {
          user: message.author,
          guild: message.guild,
          channel: boostchannel
        });
      } catch (error) {
        return embeds.warn(message, `Invalid message format: ${error.message}`);
      }

      const isUpdate = globaldata.BoostChannel && globaldata.BoostMessage;

      globaldata.BoostChannel = boostchannel.id;
      globaldata.BoostMessage = boostmessage;
      await globaldata.save();

      const action = isUpdate ? 'Updated the' : 'Created a';
      return embeds.success(message, `${action} **boost message** for ${boostchannel}`);

    } else if (args[0] === 'remove') {

      const channelInput = args[1];
      if (!channelInput) return false;

      // Use universal finder for channel detection
      const channelResult = message.mentions.channels.first() ?
        { found: true, channel: message.mentions.channels.first() } :
        client.findChannel(message.guild, channelInput);

      if (!channelResult.found) return false;

      const boostchannel = channelResult.channel;

      if (!globaldata.BoostChannel || globaldata.BoostChannel !== boostchannel.id) {
        return embeds.warn(message, `No **boost message** exists for ${boostchannel}`);
      }

      globaldata.BoostChannel = null;
      globaldata.BoostMessage = null;
      await globaldata.save();

      return embeds.success(message, `Removed the **boost message** for ${boostchannel}`);


    } else if (args[0] === 'view') {

      const channelInput = args[1];
      if (!channelInput) return false;

      // Use universal finder for channel detection
      const channelResult = message.mentions.channels.first() ?
        { found: true, channel: message.mentions.channels.first() } :
        client.findChannel(message.guild, channelInput);

      if (!channelResult.found) return false;

      const boostchannel = channelResult.channel;

      if (!globaldata.BoostChannel || globaldata.BoostChannel !== boostchannel.id || !globaldata.BoostMessage) {
        return embeds.warn(message, `No **boost message** to preview for ${boostchannel}`);
      }

      // Use EmbedVariableProcessor to handle the boost message with variables
      EmbedVariableProcessor.process(message.channel, globaldata.BoostMessage, {
        user: message.author,
        guild: message.guild,
        channel: boostchannel
      });

    } else if (args[0] === 'variables') {

      const member = message.author;
      const ordinal = (message.guild.memberCount.toString().endsWith(1) && !message.guild.memberCount.toString().endsWith(11)) ? 'st' : (message.guild.memberCount.toString().endsWith(2) && !message.guild.memberCount.toString().endsWith(12)) ? 'nd' : (message.guild.memberCount.toString().endsWith(3) && !message.guild.memberCount.toString().endsWith(13)) ? 'rd' : 'th';

      const embed = new EmbedBuilder()
        .setColor(config.colors.info)
        .setTitle('Boost Message Variables')
        .setDescription(`\`{user.id}\` — ${member.id}\n\`{user.name}\` — ${member.username}\n\`{user.mention}\` — ${member}\n\`{user.tag}\` — ${member.tag}\n\`{server.name}\` — ${message.guild.name}\n\`{server.membercount}\` — ${message.guild.memberCount}\n\`{server.humanmembercount}\` — ${message.guild.memberCount + ordinal}\n\`{server.boostcount}\` — ${message.guild.premiumSubscriptionCount}\n\`{server.boostlevel}\` — ${message.guild.premiumTier}`);

      return message.channel.send({ embeds: [embed] });

    } else if (args[0] === 'check') {

      if (!globaldata.BoostChannel || !globaldata.BoostMessage) {
        return embeds.warn(message, 'No **boost message** has been setup yet');
      }

      const boostchannel = message.guild.channels.cache.get(globaldata.BoostChannel);

      if (!boostchannel) {
        return embeds.warn(message, 'The boost channel no longer exists');
      }

      const embed = new EmbedBuilder()
        .setColor(config.colors.info)
        .addFields(
          { name: 'Boost Channel', value: `> ${boostchannel}`, inline: true },
          { name: 'Message', value: globaldata.BoostMessage.length > 1014 ? '```msg\n' + globaldata.BoostMessage.substring(0, 1011) + '...```' : '```msg\n' + globaldata.BoostMessage + '```', inline: false }
        );

      return message.channel.send({ embeds: [embed] });

    } else {
      // Invalid subcommand - show help
      return false;
    }
  }
}
