const { PermissionFlagsBits } = require("discord.js");
const { embeds } = require('../../../core/embeds');
const { hasDiscordPermission } = require('../../../utils/permissions');
const { runHelpCommand } = require('../../../utils/commandProcessor');

module.exports = {
  name: "pin",
  description: 'pin a message in the current channel',
  usage: '{guildprefix}pin [message id]',
  permission: "Manage Messages",
  run: async(client, message, args) => {

    if (!hasDiscordPermission(message, PermissionFlagsBits.ManageMessages, 'Manage Messages')) return;

    if (!message.guild.members.me.permissions.has(PermissionFlagsBits.ManageMessages)) {
      return embeds.deny(message, 'I need **Manage Messages** permission to use this command');
    }

    if (!args[0]) {
      return runHelpCommand(message, 'pin');
    }

    const messageId = args[0];

    try {
      // Try to fetch the message
      const targetMessage = await message.channel.messages.fetch(messageId);
      
      // Check if message is already pinned
      if (targetMessage.pinned) {
        return embeds.warn(message, 'That message is already pinned');
      }

      // Pin the message
      await targetMessage.pin();
      
      return embeds.success(message, `Successfully **pinned** the message`);

    } catch (error) {
      if (error.code === 10008) {
        return embeds.warn(message, 'Message not found. Make sure the message ID is correct and from this channel.');
      } else if (error.code === 30003) {
        return embeds.warn(message, 'Cannot pin message. This channel has reached the maximum number of pinned messages (50).');
      } else {
        return embeds.warn(message, 'An error occurred while trying to pin the message.');
      }
    }
  }
}
