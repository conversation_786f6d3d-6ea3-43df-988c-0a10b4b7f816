const { EmbedBuilder } = require('discord.js');
const { embeds } = require('../../core/embeds');
const { ensureGuildData } = require('../../database/global');
const { EmbedVariableProcessor } = require('../../utils/embedbuilder');
const config = require('../../config/setup');

module.exports = {
  name: "leave",
  aliases: ['bye'],
  description: `Set up a leave message for when members leave`,
  usage: '{guildprefix}leave add [channel] [message]\n{guildprefix}leave remove [channel]\n{guildprefix}leave view [channel]\n{guildprefix}leave check\n\n{guildprefix}leave variables',
  permission: "Administrator",

  run: async(client, message, args) => {

    // Show help if no subcommand provided
    if (!args[0]) return false;

    // Ensure guild data exists in the database
    const globaldata = await ensureGuildData(message.guild.id);

    if (args[0] === 'add') {

      const channelInput = args[1];
      const leavemessage = args.slice(2).join(' ');

      if (!channelInput || !leavemessage) {
        return false;
      }

      // Use universal finder for channel detection
      const channelResult = message.mentions.channels.first() ?
        { found: true, channel: message.mentions.channels.first() } :
        client.findChannel(message.guild, channelInput);

      if (!channelResult.found) {
        return false;
      }

      const leavechannel = channelResult.channel;

      // Process the message using EmbedVariableProcessor for embed/text detection
      try {
        EmbedVariableProcessor.process(message.channel, leavemessage, {
          user: message.author,
          guild: message.guild,
          channel: leavechannel
        });
      } catch (error) {
        return embeds.warn(message, `Invalid message format: ${error.message}`);
      }

      // Check if this is an update or new addition
      const isUpdate = globaldata.LeaveChannel && globaldata.LeaveMessage;

      // Update both channel and message (will create or update existing)
      globaldata.LeaveChannel = leavechannel.id;
      globaldata.LeaveMessage = leavemessage;
      await globaldata.save();

      const action = isUpdate ? 'Updated the' : 'Created a';
      return embeds.success(message, `${action} **leave message** for ${leavechannel}`);

    } else if (args[0] === 'remove') {

      const channelInput = args[1];
      if (!channelInput) {
        return false;
      }

      // Use universal finder for channel detection
      const channelResult = message.mentions.channels.first() ?
        { found: true, channel: message.mentions.channels.first() } :
        client.findChannel(message.guild, channelInput);

      if (!channelResult.found) {
        return false;
      }

      const leavechannel = channelResult.channel;

      if (!globaldata.LeaveChannel || globaldata.LeaveChannel !== leavechannel.id) {
        return embeds.warn(message, `No **leave message** exists for ${leavechannel}`);
      }

      globaldata.LeaveChannel = null;
      globaldata.LeaveMessage = null;
      await globaldata.save();

      return embeds.success(message, `Removed the **leave message** for ${leavechannel}`);

    } else if (args[0] === 'view') {

      const channelInput = args[1];
      if (!channelInput) {
        return false;
      }

      // Use universal finder for channel detection
      const channelResult = message.mentions.channels.first() ?
        { found: true, channel: message.mentions.channels.first() } :
        client.findChannel(message.guild, channelInput);

      if (!channelResult.found) {
        return false;
      }

      const leavechannel = channelResult.channel;

      if (!globaldata.LeaveChannel || globaldata.LeaveChannel !== leavechannel.id || !globaldata.LeaveMessage) {
        return embeds.warn(message, `No **leave message** to preview for ${leavechannel}`);
      }

      // Use EmbedVariableProcessor to handle the leave message with variables
      EmbedVariableProcessor.process(message.channel, globaldata.LeaveMessage, {
        user: message.author,
        guild: message.guild,
        channel: leavechannel
      });

    } else if (args[0] === 'variables') {

      const member = message.author;
      const ordinal = (message.guild.memberCount.toString().endsWith(1) && !message.guild.memberCount.toString().endsWith(11)) ? 'st' : (message.guild.memberCount.toString().endsWith(2) && !message.guild.memberCount.toString().endsWith(12)) ? 'nd' : (message.guild.memberCount.toString().endsWith(3) && !message.guild.memberCount.toString().endsWith(13)) ? 'rd' : 'th';

      const embed = new EmbedBuilder()
        .setColor(config.colors.info)
        .setTitle('Leave Message Variables')
        .setDescription(`\`{user.id}\` — ${member.id}\n\`{user.name}\` — ${member.username}\n\`{user.mention}\` — ${member}\n\`{user.tag}\` — ${member.tag}\n\`{server.name}\` — ${message.guild.name}\n\`{server.membercount}\` — ${message.guild.memberCount}\n\`{server.humanmembercount}\` — ${message.guild.memberCount + ordinal}`);

      return message.channel.send({ embeds: [embed] });

    } else if (args[0] === 'check') {

      if (!globaldata.LeaveChannel || !globaldata.LeaveMessage) {
        return embeds.warn(message, 'No **leave message** has been setup yet');
      }

      const leavechannel = message.guild.channels.cache.get(globaldata.LeaveChannel);

      if (!leavechannel) {
        return embeds.warn(message, 'The leave channel no longer exists');
      }

      const embed = new EmbedBuilder()
        .setColor(config.colors.info)
        .addFields(
          { name: 'Leave channel', value: `> ${leavechannel}`, inline: true },
          { name: 'Message', value: globaldata.LeaveMessage.length > 1014 ? '```msg\n' + globaldata.LeaveMessage.substring(0, 1011) + '...```' : '```msg\n' + globaldata.LeaveMessage + '```', inline: false }
        );

      return message.channel.send({ embeds: [embed] });

    } else {
      // Invalid subcommand - show help
      return false;
    }
  }
}
