const { EmbedBuilder } = require('discord.js');
const { embeds } = require('../../core/embeds');
const { ensureGuildData } = require('../../database/global');
const { checkBotRoleHierarchy } = require('../../utils/permissions');
const { EmbedVariableProcessor } = require('../../utils/embedbuilder');
const config = require('../../config/setup');

module.exports = {
  name: "vanity",
  aliases: ['rep'],
  description: `add vanity role to someone for reppin your server in their custom status`,
  usage: `{guildprefix}vanity set [vanity]\n{guildprefix}vanity set none\n\n{guildprefix}vanity role [@role]\n{guildprefix}vanity role none\n\n{guildprefix}vanity add [channel] [message]\n{guildprefix}vanity remove [channel]\n{guildprefix}vanity view [channel]\n\n{guildprefix}vanity check\n{guildprefix}vanity test\n{guildprefix}vanity variables`,
  permission: "Administrator",
  run: async(client, message, args) => {

    // Show help if no subcommand provided
    if (!args[0]) return false;

    // Ensure guild data exists in the database
    const globaldata = await ensureGuildData(message.guild.id);

    if (args[0] === 'set') {
      if (!args[1]) return false;

      if (args[1] === 'none' || args[1] === 'off') {
        if (!globaldata.Vanity) {
          return embeds.warn(message, 'No vanity exists to reset');
        }

        globaldata.Vanity = null;
        await globaldata.save();
        return embeds.success(message, 'Vanity has been reset');
      } else {
        const vanitytext = args.slice(1).join(' ');
        globaldata.Vanity = vanitytext;
        await globaldata.save();
        return embeds.success(message, `Set **${vanitytext}** as the vanity`);
      }

    } else if (args[0] === 'role') {
      if (!args[1]) return false;

      if (args[1] === 'none' || args[1] === 'clear') {
        if (globaldata.VanityRoles.length === 0) {
          return embeds.warn(message, 'No vanity roles exist to clear');
        }

        globaldata.VanityRoles = [];
        await globaldata.save();
        return embeds.success(message, 'All vanity roles have been cleared');
      } else {
        // Use role finder for role detection
        const roleName = args.slice(1).join(' ');
        const roleResult = message.mentions.roles.first() ?
          { found: true, role: message.mentions.roles.first() } :
          client.findRole(message.guild, roleName);

        if (!roleResult.found) {
          return embeds.warn(message, roleResult.error);
        }

        const role = roleResult.role;

        // Check if bot can manage this role
        if (!checkBotRoleHierarchy(message, role)) {
          return;
        }

        if (globaldata.VanityRoles.includes(role.id)) {
          return embeds.warn(message, `**${role.name}** is already a vanity role`);
        }

        globaldata.VanityRoles.push(role.id);
        await globaldata.save();
        return embeds.success(message, `**${role.name}** is now a vanity role`);
      }

    } else if (args[0] === 'add') {

      const channelInput = args[1];
      const vanitymessage = args.slice(2).join(' ');

      if (!channelInput || !vanitymessage) return false;

      // Use universal finder for channel detection
      const channelResult = message.mentions.channels.first() ?
        { found: true, channel: message.mentions.channels.first() } :
        client.findChannel(message.guild, channelInput);

      if (!channelResult.found) return false;

      const vanitychannel = channelResult.channel;

      // Process the message using EmbedVariableProcessor for embed/text detection
      try {
        EmbedVariableProcessor.process(message.channel, vanitymessage, {
          user: message.author,
          guild: message.guild,
          channel: vanitychannel,
          vanity: globaldata.Vanity || 'example',
          roles: 'Example Role',
          source: 'custom status'
        });
      } catch (error) {
        return embeds.warn(message, `Invalid message format: ${error.message}`);
      }

      // Check if this is an update or new addition
      const isUpdate = globaldata.VanityLogChannel && globaldata.VanityMessage;
      globaldata.VanityLogChannel = vanitychannel.id;
      globaldata.VanityMessage = vanitymessage;
      await globaldata.save();

      const action = isUpdate ? 'Updated the' : 'Created a';
      return embeds.success(message, `${action} **vanity message** for ${vanitychannel}`);

    } else if (args[0] === 'remove') {

      const channelInput = args[1];
      if (!channelInput) return false;

      // Use universal finder for channel detection
      const channelResult = message.mentions.channels.first() ?
        { found: true, channel: message.mentions.channels.first() } :
        client.findChannel(message.guild, channelInput);

      if (!channelResult.found) return false;

      const vanitychannel = channelResult.channel;

      if (!globaldata.VanityLogChannel || globaldata.VanityLogChannel !== vanitychannel.id) {
        return embeds.warn(message, `No **vanity message** exists for ${vanitychannel}`);
      }

      globaldata.VanityLogChannel = null;
      globaldata.VanityMessage = null;
      await globaldata.save();

      return embeds.success(message, `Removed the **vanity message** for ${vanitychannel}`);

    } else if (args[0] === 'view') {

      const channelInput = args[1];
      if (!channelInput) return false;

      // Use universal finder for channel detection
      const channelResult = message.mentions.channels.first() ?
        { found: true, channel: message.mentions.channels.first() } :
        client.findChannel(message.guild, channelInput);

      if (!channelResult.found) return false;

      const vanitychannel = channelResult.channel;

      if (!globaldata.VanityLogChannel || globaldata.VanityLogChannel !== vanitychannel.id || !globaldata.VanityMessage) {
        return embeds.warn(message, `No **vanity message** to preview for ${vanitychannel}`);
      }

      // Use EmbedVariableProcessor to handle the vanity message with variables
      EmbedVariableProcessor.process(message.channel, globaldata.VanityMessage, {
        user: message.author,
        guild: message.guild,
        channel: vanitychannel,
        vanity: globaldata.Vanity || 'example',
        roles: 'Example Role',
        source: 'custom status'
      });

    } else if (args[0] === 'variables') {
      const embed = new EmbedBuilder()
        .setColor(config.colors.embed)
        .setTitle('Vanity Message Variables')
        .setDescription(`**User Variables:**\n\`{user.mention}\` — ${message.member}\n\`{user.name}\` — ${message.author.username}\n\`{user.tag}\` — ${message.author.tag}\n\`{user.id}\` — ${message.author.id}\n\n**Server Variables:**\n\`{guild.name}\` — ${message.guild.name}\n\`{guild.count}\` — ${message.guild.memberCount}\n\n**Vanity Variables:**\n\`{vanity}\` — The vanity text that was detected\n\`{roles}\` — The roles that were given\n\`{source}\` — Always "custom status"`);

      return message.channel.send({ embeds: [embed] });

    } else if (args[0] === 'check') {
      // Format vanity display
      const vanityDisplay = globaldata.Vanity || '`none`';

      // Format roles display
      let rolesDisplay = '';
      if (globaldata.VanityRoles && globaldata.VanityRoles.length > 0) {
        globaldata.VanityRoles.forEach(roleId => {
          rolesDisplay += `<@&${roleId}> `;
        });
      } else {
        rolesDisplay = '`none`';
      }

      // Add status information
      const isConfigured = globaldata.Vanity && globaldata.VanityRoles && globaldata.VanityRoles.length > 0;
      const statusDisplay = isConfigured ? '✅ **Active** (detects custom status)' : '❌ **Inactive** (missing vanity text or roles)';

      const embed = new EmbedBuilder()
        .setColor(config.colors.embed)
        .setTitle('Vanity Settings')
        .addFields(
          { name: '**Status**', value: `> ${statusDisplay}`, inline: false },
          { name: '**Vanity**', value: `> ${vanityDisplay}`, inline: false },
          { name: '**Roles**', value: `> ${rolesDisplay}`, inline: false }
        );

      // Add message info if configured
      if (globaldata.VanityLogChannel && globaldata.VanityMessage) {
        const vanitychannel = message.guild.channels.cache.get(globaldata.VanityLogChannel);
        if (vanitychannel) {
          embed.addFields(
            { name: 'Vanity Channel', value: `> ${vanitychannel}`, inline: true },
            { name: 'Message', value: globaldata.VanityMessage.length > 1014 ? '```msg\n' + globaldata.VanityMessage.substring(0, 1011) + '...```' : '```msg\n' + globaldata.VanityMessage + '```', inline: false }
          );
        }
      }

      return message.channel.send({ embeds: [embed] });

    } else if (args[0] === 'test') {
      // Test vanity detection manually
      if (!globaldata.Vanity || !globaldata.VanityRoles || globaldata.VanityRoles.length === 0) {
        return embeds.deny(message, 'Vanity system is not configured. Set a vanity text and roles first');
      }

      // Check if user already has vanity roles
      const hasVanityRole = globaldata.VanityRoles.some(roleId =>
        message.member.roles.cache.has(roleId)
      );

      if (hasVanityRole) {
        return embeds.warn(message, 'You already have a vanity role. Remove it first to test');
      }

      // Simulate vanity detection
      const rolesToAdd = [];
      for (const roleId of globaldata.VanityRoles) {
        const role = message.guild.roles.cache.get(roleId);
        if (role) {
          if (message.guild.members.me.roles.highest.position > role.position) {
            rolesToAdd.push(role);
          }
        }
      }

      if (rolesToAdd.length === 0) {
        return embeds.deny(message, 'No valid roles to add. Check bot permissions and role hierarchy');
      }

      try {
        await message.member.roles.add(rolesToAdd, `Vanity test by ${message.author.tag}`);
        return embeds.success(message, `**Test successful!** Added roles: ${rolesToAdd.map(r => r.name).join(', ')}`);
      } catch (error) {
        return embeds.deny(message, `Failed to add roles: ${error.message}`);
      }

    } else {
      // Invalid subcommand - show help
      return false;
    }
  }
}
