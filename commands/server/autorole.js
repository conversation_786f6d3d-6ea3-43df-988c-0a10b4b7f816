const { EmbedBuilder, PermissionFlagsBits } = require('discord.js');
const { embeds } = require('../../core/embeds');
const { hasDiscordPermission, checkUserRoleHierarchy, checkBotRoleHierarchy } = require('../../utils/permissions');
const { runHelpCommand } = require('../../utils/commandProcessor');
const { ensureGuildData } = require('../../database/global');
const { createConfirmation } = require('../../core/buttons');
const { Autorole } = require('../../database');
const config = require('../../config/setup');

module.exports = {
  name: "autorole",
  aliases: ['ar'],
  description: `Set up automatic role assign on member join`,
  usage: '{guildprefix}autorole add [role]\n{guildprefix}autorole remove [role] \n{guildprefix}autorole list \n{guildprefix}autorole reset',
  permission: 'Manage Roles',
  run: async(client, message, args) => {

    if (!hasDiscordPermission(message, PermissionFlagsBits.ManageRoles, 'Manage Roles')) return;

    await ensureGuildData(message.guild.id);

    if (args[0] === 'add') {

      const roleName = args.slice(1).join(' ');

      if (!roleName) {
        return runHelpCommand(message, 'autorole');
      }

      const roleResult = message.mentions.roles.first() ?
        { found: true, role: message.mentions.roles.first() } :
        client.findRole(message.guild, roleName);

      if (!roleResult.found) {
        return embeds.deny(message, roleResult.error);
      }

      const role = roleResult.role;

      // Hierarchy checks
      if (!checkUserRoleHierarchy(message, role)) return;
      if (!checkBotRoleHierarchy(message, role)) return;

      let autoroledata = await Autorole.findOne({ GuildID: message.guild.id });

      if (!autoroledata) {
        // Create new autorole document if none exists
        autoroledata = new Autorole({
          GuildID: message.guild.id,
          RoleIDs: []
        });
      }

      // Check if role is already in autoroles
      if (autoroledata.RoleIDs.includes(role.id)) {
        return embeds.warn(message, `**${role.name}** is already an auto role`);
      }

      // Check if we've reached the 10 role limit
      if (autoroledata.RoleIDs.length >= 10) {
        return embeds.deny(message, 'You have added **max autoroles (10)**. Please remove some first.');
      }

      // Add the new role
      autoroledata.RoleIDs.push(role.id);
      await autoroledata.save();

      return embeds.success(message, `**${role.name}** has been added to auto roles`);



    } else if (args[0] === 'reset') {

      const autoroledata = await Autorole.findOne({ GuildID: message.guild.id });

      if (autoroledata && autoroledata.RoleIDs.length > 0) {
        // Show confirmation dialog
        await createConfirmation(
          message,
          'Are you sure you want to **remove all auto roles**?',
          async (interaction) => {
            // On approve: delete all auto roles
            await Autorole.findOneAndRemove({ GuildID: message.guild.id });

            // Edit the confirmation embed to show success message
            const successEmbed = new EmbedBuilder()
              .setColor(config.colors.success)
              .setDescription(`✅ <@${message.author.id}>: Removed all auto roles`);

            await interaction.editReply({ embeds: [successEmbed], components: [] });
          }
        );
      } else {
        return embeds.warn(message, 'No auto roles found');
      }



    } else if (args[0] === 'list') {

      const autoroledata = await Autorole.findOne({ GuildID: message.guild.id });

      if (autoroledata && autoroledata.RoleIDs.length > 0) {
        // Clean up invalid roles before displaying
        const validRoles = [];
        for (const roleId of autoroledata.RoleIDs) {
          const role = message.guild.roles.cache.get(roleId);
          if (role) {
            validRoles.push(roleId);
          } else {
            // Remove invalid role from database
            autoroledata.RoleIDs = autoroledata.RoleIDs.filter(id => id !== roleId);
          }
        }

        // Save cleaned data if roles were removed
        if (validRoles.length !== autoroledata.RoleIDs.length) {
          if (validRoles.length === 0) {
            await Autorole.findOneAndRemove({ GuildID: message.guild.id });
            return embeds.warn(message, 'No auto roles found');
          } else {
            autoroledata.RoleIDs = validRoles;
            await autoroledata.save();
          }
        }

        const roleList = validRoles.map((roleId, index) => {
          const role = message.guild.roles.cache.get(roleId);
          const roleNumber = (index + 1).toString().padStart(2, '0');
          return `\`${roleNumber}\` **${role.name}** (\`${roleId}\`)`;
        }).join('\n');

        const embed = new EmbedBuilder()
          .setColor(config.colors.embed)
          .setTitle('Auto Roles')
          .setDescription(roleList)
          .setFooter({ text: `${validRoles.length}/10 auto roles` });

        return message.channel.send({ embeds: [embed] });

      } else {
        return embeds.warn(message, 'No auto roles found');
      }



    } else if (args[0] === 'remove' || args[0] === 'rm' || args[0] === 'delete' || args[0] === 'del') {

      const roleName = args.slice(1).join(' ');

      if (!roleName) {
        return runHelpCommand(message, 'autorole');
      }

      const roleResult = message.mentions.roles.first() ?
        { found: true, role: message.mentions.roles.first() } :
        client.findRole(message.guild, roleName);

      if (!roleResult.found) {
        return embeds.deny(message, roleResult.error);
      }

      const role = roleResult.role;
      const autoroledata = await Autorole.findOne({ GuildID: message.guild.id });

      if (autoroledata && autoroledata.RoleIDs.length > 0) {
        if (!autoroledata.RoleIDs.includes(role.id)) {
          return embeds.deny(message, `**${role.name}** is not an auto role`);
        }

        // Remove the role from the array
        autoroledata.RoleIDs = autoroledata.RoleIDs.filter(roleId => roleId !== role.id);

        // If no roles left, delete the document
        if (autoroledata.RoleIDs.length === 0) {
          await Autorole.findOneAndRemove({ GuildID: message.guild.id });
        } else {
          await autoroledata.save();
        }

        return embeds.success(message, `Removed **${role.name}** from auto roles`);
      } else {
        return embeds.deny(message, `No auto roles found`);
      }

    } else {
      return runHelpCommand(message, 'autorole');
    }
      
  }
}
