const { EmbedBuilder, PermissionFlagsBits } = require('discord.js');
const { embeds } = require('../../core/embeds');
const { hasDiscordPermission } = require('../../utils/permissions');
const { runHelpCommand } = require('../../utils/commandProcessor');
const { ensureGuildData } = require('../../database/global');
const { createPagination, createConfirmation } = require('../../core/buttons');
const { Autoresponder } = require('../../database');
const config = require('../../config/setup');

module.exports = {
  name: "autoresponder",
  aliases: ['autoresponse', 'autorespond', 'trigger'],
  description: `automatically respond to specific triggers`,
  usage: '{guildprefix}autoresponder add [trigger], [response] [--options]\n{guildprefix}autoresponder remove [trigger]\n{guildprefix}autoresponder list\n{guildprefix}autoresponder reset',
  permission: 'Manage Channels',
  run: async(client, message, args) => {

    if (!hasDiscordPermission(message, PermissionFlagsBits.ManageChannels, 'Manage Channels')) return;

    // Ensure guild data exists in the database
    await ensureGuildData(message.guild.id);

    if (args[0] === 'add' || args[0] === 'create') {

      const fullInput = args.slice(1).join(' ');

      // Handle cases where trigger starts with comma
      let commaIndex;
      if (fullInput.startsWith(',')) {
        // If input starts with comma, find the second comma
        commaIndex = fullInput.indexOf(',', 1);
      } else {
        // Normal case: find first comma
        commaIndex = fullInput.indexOf(',');
      }

      if (commaIndex === -1) {
        return runHelpCommand(message, 'autoresponder');
      }

      const trigger = fullInput.substring(0, commaIndex).trim();
      const responseAndOptions = fullInput.substring(commaIndex + 1).trim();

      // Parse options from the response
      const optionRegex = /--(reply|strict|not_strict|delete|self_destruct)\s*(\d+)?/g;
      let response = responseAndOptions;
      let options = {
        reply: false,
        strict: true,
        delete: false,
        selfDestruct: 0
      };

      let match;
      while ((match = optionRegex.exec(responseAndOptions)) !== null) {
        const option = match[1];
        const value = match[2];

        // Remove the option from response
        response = response.replace(match[0], '').trim();

        switch (option) {
          case 'reply':
            options.reply = true;
            break;
          case 'strict':
            options.strict = true;
            break;
          case 'not_strict':
            options.strict = false;
            break;
          case 'delete':
            options.delete = true;
            break;
          case 'self_destruct':
            options.selfDestruct = parseInt(value) || 5;
            break;
        }
      }

      if (!trigger || !response) {
        return runHelpCommand(message, 'autoresponder');
      }

      if (trigger.startsWith(',')) {
        const commandName = trigger.substring(1); // Remove the comma
        const isCommand = client.commands.has(commandName.toLowerCase());
        const isAlias = client.aliases.has(commandName.toLowerCase());

        if (isCommand || isAlias) {
          return embeds.warn(message, `\`${commandName}\` is already a **bot command**, can't use this`);
        }
      }

      const data = await Autoresponder.findOne({ GuildID: message.guild.id, Trigger: trigger });

      if (data) {
        return embeds.deny(message, `An autoresponder for **${trigger}** already exists`);
      } else {
        const newdata = new Autoresponder({
          GuildID: message.guild.id,
          Trigger: trigger,
          Response: response,
          Reply: options.reply,
          Strict: options.strict,
          Delete: options.delete,
          SelfDestruct: options.selfDestruct
        });

        await newdata.save();

        // Build options display
        let optionsDisplay = [];
        if (options.reply) optionsDisplay.push('reply');
        if (!options.strict) optionsDisplay.push('not_strict');
        if (options.delete) optionsDisplay.push('delete');
        if (options.selfDestruct > 0) optionsDisplay.push(`self_destruct ${options.selfDestruct}s`);

        const optionsText = optionsDisplay.length > 0 ? ` with options: ${optionsDisplay.join(', ')}` : '';
        return embeds.success(message, `Created autoresponder for **${trigger}**${optionsText}`);
      }


      
    } else if (args[0] === 'reset') {

      const data = await Autoresponder.findOne({ GuildID: message.guild.id });

      if (data) {
        // Show confirmation dialog
        await createConfirmation(
          message,
          'Are you sure you want to **remove all autoresponders**?',
          async (interaction) => {
            // On approve: delete all autoresponders
            await Autoresponder.deleteMany({ GuildID: message.guild.id });

            // Edit the confirmation embed to show success message
            const successEmbed = new EmbedBuilder()
              .setColor(config.colors.success)
              .setDescription(`✅ <@${message.author.id}>: Removed all autoresponders`);

            await interaction.editReply({ embeds: [successEmbed], components: [] });
          }
        );
      } else {
        return embeds.warn(message, 'No autoresponders found');
      }



    } else if (args[0] === 'list') {

      const data = await Autoresponder.find({ GuildID: message.guild.id });

      if (data && data.length > 0) {
        // Format page function for pagination
        const formatPage = (pageAutoresponders, currentPage, totalPages) => {
          const startIndex = (currentPage - 1) * 8;

          const autoresponderList = pageAutoresponders.map((autoresponder, index) => {
            const number = (startIndex + index + 1).toString().padStart(2, '0');

            // Build options display
            let optionsDisplay = [];
            if (autoresponder.Reply) optionsDisplay.push('reply');
            if (!autoresponder.Strict) optionsDisplay.push('not_strict');
            if (autoresponder.Delete) optionsDisplay.push('delete');
            if (autoresponder.SelfDestruct > 0) optionsDisplay.push(`self_destruct ${autoresponder.SelfDestruct}s`);

            const optionsText = optionsDisplay.length > 0 ? ` \`[${optionsDisplay.join(', ')}]\`` : '';
            return `\`${number}\` **${autoresponder.Trigger}** → ${autoresponder.Response}${optionsText}`;
          }).join('\n');

          return new EmbedBuilder()
            .setColor(config.colors.embed)
            .setTitle('Autoresponders')
            .setDescription(autoresponderList)
            .setFooter({ text: `${data.length} autoresponders • Page ${currentPage}/${totalPages}` });
        };

        // Use pagination system with 8 items per page
        await createPagination(message, data, formatPage, 8, 'Autoresponders');
      } else {
        return embeds.warn(message, 'No autoresponders found');
      }



    } else if (args[0] === 'remove' || args[0] === 'delete' || args[0] === 'rm' || args[0] === 'del') {

      const trigger = args[1];

      if (!trigger) {
        return runHelpCommand(message, 'autoresponder');
      }

      const data = await Autoresponder.findOne({ GuildID: message.guild.id, Trigger: trigger });

      if (data) {
        await Autoresponder.findOneAndRemove({ GuildID: message.guild.id, Trigger: trigger });
        return embeds.success(message, `Removed autoresponder for **${trigger}**`);
      } else {
        return embeds.deny(message, `No autoresponder found for **${trigger}**`);
      }

    } else {
      return runHelpCommand(message, 'autoresponder');
    }
  }
}