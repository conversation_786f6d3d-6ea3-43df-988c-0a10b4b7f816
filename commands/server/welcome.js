const { EmbedBuilder } = require('discord.js');
const { embeds } = require('../../core/embeds');
const { ensureGuildData } = require('../../database/global');
const { EmbedVariableProcessor } = require('../../utils/embedbuilder');
const config = require('../../config/setup');

module.exports = {
  name: "welcome",
  aliases: ['welc'],
  description: `Set up a welcome message for when new members join`,
  usage: '{guildprefix}welcome add [channel] [message]\n{guildprefix}welcome remove [channel]\n{guildprefix}welcome view [channel]\n{guildprefix}welcome check\n\n{guildprefix}welcome variables',
  permission: "Administrator",

  run: async(client, message, args) => {

    if (!args[0]) return false;

    // Ensure guild data exists in the database
    const globaldata = await ensureGuildData(message.guild.id);

    if (args[0] === 'add') {

      const channelInput = args[1];
      const welcomemessage = args.slice(2).join(' ');

      if (!channelInput || !welcomemessage) return false;

      // Use universal finder for channel detection
      const channelResult = message.mentions.channels.first() ?
        { found: true, channel: message.mentions.channels.first() } :
        client.findChannel(message.guild, channelInput);

      if (!channelResult.found) return false;

      const welcomechannel = channelResult.channel;

      // Process the message using EmbedVariableProcessor for embed/text detection
      try {
        EmbedVariableProcessor.process(message.channel, welcomemessage, {
          user: message.author,
          guild: message.guild,
          channel: welcomechannel
        });
      } catch (error) {
        return embeds.warn(message, `Invalid message format: ${error.message}`);
      }

      // Check if this is an update or new addition
      const isUpdate = globaldata.WelcomeChannel && globaldata.WelcomeMessage;
      globaldata.WelcomeChannel = welcomechannel.id;
      globaldata.WelcomeMessage = welcomemessage;
      await globaldata.save();

      const action = isUpdate ? 'Updated the' : 'Created a';
      return embeds.success(message, `${action} **welcome message** for ${welcomechannel}`);

    } else if (args[0] === 'remove') {

      const channelInput = args[1];
      if (!channelInput) return false;

      // Use universal finder for channel detection
      const channelResult = message.mentions.channels.first() ?
        { found: true, channel: message.mentions.channels.first() } :
        client.findChannel(message.guild, channelInput);

      if (!channelResult.found) return false;

      const welcomechannel = channelResult.channel;

      if (!globaldata.WelcomeChannel || globaldata.WelcomeChannel !== welcomechannel.id) {
        return embeds.warn(message, `No **welcome message** exists for ${welcomechannel}`);
      }

      globaldata.WelcomeChannel = null;
      globaldata.WelcomeMessage = null;
      await globaldata.save();

      return embeds.success(message, `Removed the **welcome message** for ${welcomechannel}`);


    } else if (args[0] === 'view') {

      const channelInput = args[1];
      if (!channelInput) return false;

      // Use universal finder for channel detection
      const channelResult = message.mentions.channels.first() ?
        { found: true, channel: message.mentions.channels.first() } :
        client.findChannel(message.guild, channelInput);

      if (!channelResult.found) return false;

      const welcomechannel = channelResult.channel;

      if (!globaldata.WelcomeChannel || globaldata.WelcomeChannel !== welcomechannel.id || !globaldata.WelcomeMessage) {
        return embeds.warn(message, `No **welcome message** to preview for ${welcomechannel}`);
      }

      // Use EmbedVariableProcessor to handle the welcome message with variables
      EmbedVariableProcessor.process(message.channel, globaldata.WelcomeMessage, {
        user: message.author,
        guild: message.guild,
        channel: welcomechannel
      });

    } else if (args[0] === 'variables') {

      const member = message.author;
      const ordinal = (message.guild.memberCount.toString().endsWith(1) && !message.guild.memberCount.toString().endsWith(11)) ? 'st' : (message.guild.memberCount.toString().endsWith(2) && !message.guild.memberCount.toString().endsWith(12)) ? 'nd' : (message.guild.memberCount.toString().endsWith(3) && !message.guild.memberCount.toString().endsWith(13)) ? 'rd' : 'th';

      const embed = new EmbedBuilder()
        .setColor(config.colors.info)
        .setTitle('Welcome Message Variables')
        .setDescription(`\`{user.id}\` — ${member.id}\n\`{user.name}\` — ${member.username}\n\`{user.mention}\` — ${member}\n\`{user.tag}\` — ${member.tag}\n\`{server.name}\` — ${message.guild.name}\n\`{server.membercount}\` — ${message.guild.memberCount}\n\`{server.humanmembercount}\` — ${message.guild.memberCount + ordinal}`);

      return message.channel.send({ embeds: [embed] });

    } else if (args[0] === 'check') {

      if (!globaldata.WelcomeChannel || !globaldata.WelcomeMessage) {
        return embeds.warn(message, 'No **welcome message** has been setup yet');
      }

      const welcomechannel = message.guild.channels.cache.get(globaldata.WelcomeChannel);

      if (!welcomechannel) {
        return embeds.warn(message, 'The welcome channel no longer exists');
      }

      const embed = new EmbedBuilder()
        .setColor(config.colors.info)
        .addFields(
          { name: 'Welcome Channel', value: `> ${welcomechannel}`, inline: true },
          { name: 'Message', value: globaldata.WelcomeMessage.length > 1014 ? '```msg\n' + globaldata.WelcomeMessage.substring(0, 1011) + '...```' : '```msg\n' + globaldata.WelcomeMessage + '```', inline: false }
        );

      return message.channel.send({ embeds: [embed] });

    } else {
      // Invalid subcommand - show help
      return false;
    }
  }
}
