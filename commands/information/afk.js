const { EmbedBuilder } = require("discord.js");
const { embeds } = require('../../core/embeds');
const { AFK } = require('../../database');
const { colors } = require('../../config/setup');

module.exports = {
  name: "afk",
  description: `Set an AFK status for when you are mentioned`,
  usage: '{guildprefix}afk [reason]',
  run: async(client, message, args) => {

    const content = args.join(" ") || "AFK";

    try {
      // Check if user is already AFK
      const existingAFK = await AFK.findOne({ 
        GuildID: message.guild.id, 
        UserID: message.author.id 
      });

      if (existingAFK) {
        // User is already AFK, remove AFK status
        await AFK.findOneAndDelete({
          GuildID: message.guild.id,
          UserID: message.author.id
        });

        return embeds.success(message, 'Your **AFK** status has been removed');
      } else {
        // Set user as AFK
        const afkData = new AFK({
          GuildID: message.guild.id,
          UserID: message.author.id,
          Message: content,
          TimeAgo: new Date()
        });

        await afkData.save();

        const embed = new EmbedBuilder()
          .setColor(colors.success)
          .setDescription(`<@${message.author.id}>: You're now **AFK** with the status: **${content}**`);
        
        return message.channel.send({ embeds: [embed] });
      }

    } catch (error) {
      return embeds.warn(message, 'An error occurred while setting your AFK status.');
    }
  }
}
