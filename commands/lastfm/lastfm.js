const { EmbedBuilder } = require("discord.js");
const { embeds } = require('../../core/embeds');
const config = require('../../config/setup');
const { User } = require('../../database');
const { getGuildPrefix, runHelpCommand } = require('../../utils/commandProcessor');

module.exports = {
  name: "lastfm",
  aliases: ['lf'],
  description: 'Manage your last.fm profile and settings',
  usage: '{guildprefix}lastfm howto\n{guildprefix}lastfm set [lastfm username]\n{guildprefix}lastfm unlink',
  run: async(client, message, args) => {

    const guildprefix = await getGuildPrefix(message.guild.id);
    const fmdata = await User.findOne({ UserID: message.author.id });

    if (args[0] === 'howto') {

      const embed = new EmbedBuilder()
        .setColor(config.colors.info)
        .setTitle('**Last.fm Setup Guide**')
        .addFields(
          { name: '**First**', value: `Register for a [**last.fm account**](https://www.last.fm/join)`, inline: false },
          { name: '**Second**', value: `> For **Spotify** users, [**connect here**](https://www.last.fm/settings/applications)\n> For **other music services** [**connect here**](https://www.last.fm/about/trackmymusic)`, inline: false },
          { name: '**Third**', value: `\`\`\`${guildprefix}lf set [lastfm username]\`\`\``, inline: false }
        );

      return message.channel.send({ embeds: [embed] });

    } else if (args[0] === 'set') {

      const fmuser = args[1];

      if (!fmuser) {
        return runHelpCommand(message, 'lastfm');
      }

      // Update/create user data using new User model
      await User.findOneAndUpdate(
        { UserID: message.author.id },
        { LastFMUsername: fmuser },
        { upsert: true, new: true }
      );

      return embeds.success(message, `Your **Last.fm** profile has been set to **${fmuser}**`);

    } else if (args[0] === 'unlink') {

      // Check if user actually has data linked
      if (!fmdata || !fmdata.LastFMUsername) {
        return embeds.warn(message, `You don't have a **Last.fm** linked, use \`${guildprefix}lastfm set [username]\``);
      }

      // Remove LastFM username from user data
      await User.findOneAndUpdate(
        { UserID: message.author.id },
        { LastFMUsername: null },
        { new: true }
      );

      return embeds.success(message, `Your **Last.fm** profile has been **unlinked**`);

    } else {

      return runHelpCommand(message, 'lastfm');
    }
  }
}