const { EmbedBuilder } = require('discord.js');
const config = require('../../../config/setup');
const { embeds } = require('../../../core/embeds');
const { runHelpCommand } = require('../../../utils/commandProcessor');
const { Commander } = require('./commander');

module.exports = {
  name: "dcmd",
  aliases: ['disable', 'disablecmd'],
  description: 'Disable commands or categories',
  usage: '{guildprefix}dcmd [command/category/path]\n{guildprefix}dcmd list',
  run: async(client, message, args) => {

    // Owner only - silently ignore if not owner
    if (message.author.id !== config.bot.ownerId) {
      return;
    }

    const cmdManager = new Commander();
    const input = args.join(' ').toLowerCase();

    if (!input) {
      return runHelpCommand(message, 'dcmd');
    }

    try {
      if (input === 'list') {
        return await showDisabledCommands(message, cmdManager);
      }

      // Check if input is a specific file path
      if (input.endsWith('.js')) {
        return await disableSpecificFile(message, cmdManager, input);
      }

      // Check if input is a folder path (contains /)
      if (input.includes('/')) {
        return await disableFolder(message, cmdManager, input);
      }

      // Check if input is a category
      const categories = cmdManager.getCategories();
      const category = categories.find(cat => cat.name.toLowerCase() === input);
      if (category) {
        return await disableCategory(message, cmdManager, input);
      }

      // Try to find command by name
      return await disableCommandByName(message, cmdManager, input);

    } catch (error) {

      return embeds.deny(message, `Error: ${error.message}`);
    }
  }
};

async function showDisabledCommands(message, cmdManager) {
  const commands = cmdManager.getDisabledCommands();

  if (commands.length === 0) {
    return embeds.warn(message, `There's no **Disabled Commands** currently`);
  }

  // Group by category
  const grouped = commands.reduce((groups, command) => {
    const category = command.category;
    if (!groups[category]) {
      groups[category] = [];
    }
    groups[category].push(command.name);
    return groups;
  }, {});

  let description = '**Currently Disabled Commands:**\n\n';
  for (const [category, cmds] of Object.entries(grouped)) {
    description += `**${category}:** ${cmds.join(', ')}\n`;
  }

  const embed = new EmbedBuilder()
    .setColor(config.colors.warn)
    .setTitle('Disabled Commands')
    .setDescription(description)
    .setFooter({ text: `Total: ${commands.length} commands disabled` });

  return message.channel.send({ embeds: [embed] });
}

async function disableSpecificFile(message, cmdManager, filePath) {
  // Extract command name and category from file path
  const parts = filePath.replace('.js', '').split('/');
  const commandName = parts[parts.length - 1];
  const categoryPath = parts.slice(0, -1).join('/');

  try {
    await cmdManager.disableCommand(commandName, categoryPath);
    return embeds.success(message, `Disabled command **${commandName}** from **${categoryPath}**. Use \`,reload\` to apply changes.`);
  } catch (error) {
    return embeds.deny(message, error.message);
  }
}

async function disableFolder(message, cmdManager, folderPath) {
  const enabledCommands = cmdManager.getEnabledCommands();
  const commandsInFolder = enabledCommands.filter(cmd => 
    cmd.category.toLowerCase().startsWith(folderPath.toLowerCase())
  );

  if (commandsInFolder.length === 0) {
    return embeds.warn(message, `No enabled commands found in folder **${folderPath}**`);
  }

  let disabled = 0;
  let failed = 0;

  for (const command of commandsInFolder) {
    try {
      await cmdManager.disableCommand(command.name, command.category);
      disabled++;
    } catch (error) {
      failed++;
      console.error(`Failed to disable ${command.name}:`, error);
    }
  }

  return embeds.success(message, `Disabled **${disabled}** commands from **${folderPath}**${failed > 0 ? ` (${failed} failed)` : ''}. Use \`,reload\` to apply changes.`);
}

async function disableCategory(message, cmdManager, category) {
  try {
    const results = await cmdManager.disableCategory(category);
    const successful = results.filter(r => r.success).length;
    const failed = results.filter(r => !r.success).length;
    
    return embeds.success(message, `Disabled **${successful}** commands from category **${category}**${failed > 0 ? ` (${failed} failed)` : ''}. Use \`,reload\` to apply changes.`);
  } catch (error) {
    return embeds.deny(message, error.message);
  }
}

async function disableCommandByName(message, cmdManager, commandName) {
  const enabledCommands = cmdManager.getEnabledCommands();
  const matchingCommands = enabledCommands.filter(cmd => 
    cmd.name.toLowerCase() === commandName.toLowerCase()
  );

  if (matchingCommands.length === 0) {
    return embeds.warn(message, `Command **${commandName}** not found in enabled commands`);
  }

  if (matchingCommands.length === 1) {
    const command = matchingCommands[0];
    try {
      await cmdManager.disableCommand(command.name, command.category);
      return embeds.success(message, `Disabled command **${command.name}** from **${command.category}**. Use \`,reload\` to apply changes.`);
    } catch (error) {
      return embeds.deny(message, error.message);
    }
  }

  // Multiple matches - show options
  const options = matchingCommands.map((cmd, index) => 
    `\`${index + 1}\` **${cmd.name}** from **${cmd.category}**`
  ).join('\n');

  return embeds.warn(message, `Multiple **${commandName}** commands found:\n\n${options}\n\nUse: \`dcmd ${commandName} [category]\` to specify which one`);
}
