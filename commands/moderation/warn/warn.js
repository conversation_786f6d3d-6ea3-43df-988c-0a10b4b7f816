const { PermissionFlagsBits } = require("discord.js");
const { hasDiscordPermission, checkSelfAction, checkBotAction, checkUserMemberHierarchy, checkBotMemberHierarchy } = require('../../../utils/permissions');
const { embeds } = require('../../../core/embeds');
const { runHelpCommand } = require('../../../utils/commandProcessor');
const { addModerationCase } = require('../../../database/moderation');

module.exports = {
  name: "warn",
  description: `warn a user and add it to their moderation history`,
  usage: '{guildprefix}warn [user]\n{guildprefix}warn [user] [reason]\n{guildprefix}warn list [user]',
  permission: 'Moderate Members',
  run: async(client, message, args) => {

    if (!hasDiscordPermission(message, PermissionFlagsBits.ModerateMembers, 'Moderate Members')) return;

    if (!message.guild.members.me.permissions.has(PermissionFlagsBits.ModerateMembers)) {
      return embeds.deny(message, 'I need **Moderate Members** permission to use this command');
    }

    if (!args[0]) {
      return runHelpCommand(message, 'warn');
    }

    // Handle 'warn list' subcommand
    if (args[0].toLowerCase() === 'list') {
      if (!args[1]) {
        return runHelpCommand(message, 'warn');
      }

      // Use the history command to show warn-specific history
      const historyCommand = client.commands.get('history');
      if (historyCommand) {
        return historyCommand.run(client, message, [args[1], 'warn']);
      } else {
        return embeds.deny(message, 'History command not found. Please contact an administrator.');
      }
    }

    // Use universal user finder
    const userResult = client.findUser(message.guild, args[0]);

    if (!userResult.found) {
      return embeds.deny(message, userResult.error || 'User not found');
    }

    const user = userResult.user;
    let reason = args.slice(1).join(" ");
    if (!reason) reason = 'reasons';

    // Self-action check
    if (!checkSelfAction(message, user.user, 'warn')) return;

    // Bot-action check
    if (!checkBotAction(message, user.user)) return;

    // Hierarchy checks
    if (!checkUserMemberHierarchy(message, user)) return;
    if (!checkBotMemberHierarchy(message, user)) return;

    try {
      // Add moderation case to database
      await addModerationCase(
        message.guild.id,
        user.user.id,
        message.author.id,
        'warn',
        reason
      );

      // Send success message
      embeds.success(message, `**${user.user.username}** has been **warned** for ${reason}`);

    } catch (error) {
      embeds.deny(message, `An error occurred while warning **${user.user.username}**`);
    }
  }
};
