const { PermissionFlagsBits } = require("discord.js");
const { hasDiscordPermission, checkSelfAction, checkBotAction } = require('../../../utils/permissions');
const { embeds } = require('../../../core/embeds');
const { runHelpCommand } = require('../../../utils/commandProcessor');
const { addModerationCase, getModerationCase, removeModerationCase } = require('../../../database/moderation');

module.exports = {
  name: "unwarn",
  description: `remove a warning from a user's moderation history`,
  usage: '{guildprefix}unwarn [user] [case_id]\n{guildprefix}unwarn [user] [case_id] [reason]',
  permission: 'Manage Guild',
  run: async(client, message, args) => {

    // Permission checks first - unwarn requires higher permissions
    if (!hasDiscordPermission(message, PermissionFlagsBits.ManageGuild, 'Manage Guild')) return;

    if (!message.guild.members.me.permissions.has(PermissionFlagsBits.ModerateMembers)) {
      return embeds.deny(message, 'I need **Moderate Members** permission to use this command');
    }

    // Check if user provided input
    if (!args[0] || !args[1]) {
      return runHelpCommand(message, 'unwarn');
    }

    // Use universal user finder
    const userResult = client.findUser(message.guild, args[0]);

    if (!userResult.found) {
      return embeds.deny(message, userResult.error || 'User not found');
    }

    const user = userResult.user;
    const caseId = parseInt(args[1]);
    
    // Validate case ID
    if (isNaN(caseId)) {
      return embeds.deny(message, 'Please provide a valid case ID number');
    }

    let reason = args.slice(2).join(" ");
    if (!reason) reason = 'reasons';

    // Self-action check (can't unwarn yourself)
    if (!checkSelfAction(message, user.user, 'unwarn')) return;

    // Bot-action check
    if (!checkBotAction(message, user.user)) return;

    try {
      // Get the specific moderation case
      const moderationCase = await getModerationCase(message.guild.id, caseId);
      
      if (!moderationCase) {
        return embeds.deny(message, `Case #${caseId} not found`);
      }

      // Check if the case belongs to the specified user
      if (moderationCase.userId !== user.user.id) {
        return embeds.deny(message, `Case #${caseId} does not belong to **${user.user.username}**`);
      }

      // Check if the case is actually a warning
      if (moderationCase.action !== 'warn') {
        return embeds.deny(message, `Case #${caseId} is not a warning (it's a ${moderationCase.action})`);
      }

      // Remove the warning case from the database
      await removeModerationCase(message.guild.id, caseId);

      // Add an unwarn entry to the moderation history
      await addModerationCase(
        message.guild.id,
        user.user.id,
        message.author.id,
        'unwarn',
        `Removed warning case #${caseId}: ${reason}`
      );

      // Send success message
      embeds.success(message, `**Warning case #${caseId}** has been removed from **${user.user.username}** for ${reason}`);

    } catch (error) {
      embeds.deny(message, `An error occurred while removing the warning`);
    }
  }
};
