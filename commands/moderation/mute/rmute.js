const { PermissionFlagsBits } = require("discord.js");
const { embeds } = require('../../../core/embeds');
const { hasDiscordPermission, checkSelfAction, checkBotAction, checkUserMemberHierarchy, checkBotMemberHierarchy } = require('../../../utils/permissions');
const { runHelpCommand } = require('../../../utils/commandProcessor');

module.exports = {
  name: "rmute",
  aliases: ['reactmute', 'reactionmute', 'emojimute', 'emotemute', 'emute'],
  description: `revoke someone's reaction/emote perms in this channel`,
  usage: '{guildprefix}rmute [user]\n{guildprefix}rmute [user] [reason]',
  permission: 'Manage Channels',
  run: async(client, message, args) => {

    // Permission checks first
    if (!hasDiscordPermission(message, PermissionFlagsBits.ManageChannels, 'Manage Channels')) return;

    if (!message.guild.members.me.permissions.has(PermissionFlagsBits.ManageChannels)) {
      return embeds.deny(message, 'I need **Manage Channels** permission to use this command');
    }

    // Check if user provided input
    if (!args[0]) {
      return runHelpCommand(message, 'rmute');
    }

    // Use universal user finder
    const userResult = client.findUser(message.guild, args[0]);

    if (!userResult.found) {
      return embeds.deny(message, userResult.error || 'User not found');
    }

    const user = userResult.user;
    let reason = args.slice(1).join(" ");
    if (!reason) reason = 'reason';

    // Self-action check
    if (!checkSelfAction(message, user, 'reaction mute')) return;

    // Bot-action check
    if (!checkBotAction(message, user)) return;

    // Hierarchy checks
    if (!checkUserMemberHierarchy(message, user)) return;
    if (!checkBotMemberHierarchy(message, user)) return;

    try {
      // Check if user already has reaction mute permissions removed in this channel
      const currentOverrides = message.channel.permissionOverwrites.cache.get(user.id);
      if (currentOverrides && currentOverrides.deny.has(PermissionFlagsBits.UseExternalEmojis) && currentOverrides.deny.has(PermissionFlagsBits.AddReactions)) {
        return embeds.warn(message, `**${user.user.username}** is already reaction muted in this channel`);
      }

      // Remove reaction permissions for this channel only
      await message.channel.permissionOverwrites.edit(user, {
        UseExternalEmojis: false,
        AddReactions: false
      }, { reason: `Reaction muted by ${message.author.tag}: ${reason}` });

      embeds.success(message, `**${user.user.tag}** has been **reaction muted** in this channel for ${reason}`);

    } catch (error) {
      embeds.deny(message, 'An error occurred while trying to reaction mute this user');
    }
  }
}