const { PermissionFlagsBits } = require("discord.js");
const { embeds } = require('../../../core/embeds');
const { hasDiscordPermission, checkSelfAction, checkBotAction, checkUserMemberHierarchy, checkBotMemberHierarchy } = require('../../../utils/permissions');
const { runHelpCommand } = require('../../../utils/commandProcessor');

module.exports = {
  name: "iunmute",
  aliases: ['imgunmute', 'imageunmute'],
  description: `restore someone's attachment/embed perms in this channel`,
  usage: '{guildprefix}iunmute [user]\n{guildprefix}iunmute [user] [reason]',
  permission: 'Manage Channels',
  run: async(client, message, args) => {

    // Permission checks first
    if (!hasDiscordPermission(message, PermissionFlagsBits.ManageChannels, 'Manage Channels')) return;

    if (!message.guild.members.me.permissions.has(PermissionFlagsBits.ManageChannels)) {
      return embeds.deny(message, 'I need **Manage Channels** permission to use this command');
    }

    // Check if user provided input
    if (!args[0]) {
      return runHelpCommand(message, 'iunmute');
    }

    // Use universal user finder
    const userResult = client.findUser(message.guild, args[0]);

    if (!userResult.found) {
      return embeds.deny(message, userResult.error || 'User not found');
    }

    const user = userResult.user;
    let reason = args.slice(1).join(" ");
    if (!reason) reason = 'reason';

    // Self-action check
    if (!checkSelfAction(message, user, 'image unmute')) return;

    // Bot-action check
    if (!checkBotAction(message, user)) return;

    // Hierarchy checks
    if (!checkUserMemberHierarchy(message, user)) return;
    if (!checkBotMemberHierarchy(message, user)) return;

    try {
      // Check if user has image mute permissions in this channel
      const currentOverrides = message.channel.permissionOverwrites.cache.get(user.id);
      if (!currentOverrides || (!currentOverrides.deny.has(PermissionFlagsBits.EmbedLinks) && !currentOverrides.deny.has(PermissionFlagsBits.AttachFiles))) {
        return embeds.warn(message, `**${user.user.username}** is not image muted in this channel`);
      }

      // Remove the permission overrides (restore default permissions)
      await message.channel.permissionOverwrites.edit(user, {
        EmbedLinks: null,
        AttachFiles: null
      }, { reason: `Image unmuted by ${message.author.tag}: ${reason}` });

      embeds.success(message, `**${user.user.tag}** has been **image unmuted** in this channel for ${reason}`);

    } catch (error) {
      embeds.deny(message, 'An error occurred while trying to image unmute this user');
    }
  }
}