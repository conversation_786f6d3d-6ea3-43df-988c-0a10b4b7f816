const { PermissionFlagsBits } = require("discord.js");
const { hasDiscordPermission, checkSelfAction, checkBotAction, checkDiscordActionable } = require('../../../utils/permissions');
const { embeds } = require('../../../core/embeds');
const { runHelpCommand } = require('../../../utils/commandProcessor');
const { addModerationCase } = require('../../../database/moderation');

module.exports = {
  name: "unmute",
  aliases: ['untimeout'],
  description: `remove timeout from a user using Discord's built-in timeout feature`,
  usage: '{guildprefix}unmute [user] [reason]\n{guildprefix}unmute @user appeal accepted',
  permission: 'Moderate Members',
  run: async(client, message, args) => {

    // Permission checks first
    if (!hasDiscordPermission(message, PermissionFlagsBits.ModerateMembers, 'Moderate Members')) return;

    if (!message.guild.members.me.permissions.has(PermissionFlagsBits.ModerateMembers)) {
      return embeds.deny(message, 'I need **Moderate Members** permission to use this command');
    }

    // Check if user provided input
    if (!args[0]) {
      return runHelpCommand(message, 'unmute');
    }

    // Use universal user finder
    const userResult = client.findUser(message.guild, args[0]);

    if (!userResult.found) {
      return embeds.deny(message, userResult.error || 'User not found');
    }

    const user = userResult.user;

    // Check self action
    if (!checkSelfAction(message, user.user)) return;

    // Check bot action
    if (!checkBotAction(message, user.user)) return;

    // Check if user is actionable (hierarchy and Discord restrictions)
    if (!checkDiscordActionable(message, user, 'untimeout')) return;

    // Check if user is actually timed out
    if (!user.communicationDisabledUntil || user.communicationDisabledUntil <= new Date()) {
      return embeds.warn(message, `**${user.user.username}** is not timed out`);
    }

    const reason = args.slice(1).join(' ') || 'reasons';

    try {
      await user.timeout(null, reason); // null removes the timeout

      // Add to moderation history
      try {
        await addModerationCase(
          message.guild.id,
          user.user.id,
          message.author.id,
          'unmute',
          reason
        );
      } catch (historyError) {
        // Don't fail the command if history logging fails
      }

      return embeds.success(message, `**${user.user.username}** has been **unmuted** for ${reason}`);

    } catch (error) {
      return embeds.deny(message, 'Failed to remove timeout. Please check my permissions and role hierarchy.');
    }
  }
}
