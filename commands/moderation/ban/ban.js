const { PermissionFlagsBits } = require("discord.js");
const { embeds } = require('../../../core/embeds');
const { hasDiscordPermission, checkSelfAction, checkBotAction, checkUserMemberHierarchy, checkBotMemberHierarchy, checkDiscordActionable } = require('../../../utils/permissions');
const { runHelpCommand } = require('../../../utils/commandProcessor');
const { addModerationCase } = require('../../../database/moderation');

module.exports = {
  name: "ban",
  aliases: ['deport', 'hackban', 'hban', 'hb'],
  description: 'ban the mentioned user from the server (works with user IDs for users not in server)',
  usage: '{guildprefix}ban [user]\n{guildprefix}ban [user] [reason]\n{guildprefix}ban [userID] [reason] (hackban)',
  permission: 'Ban Members',
  run: async(client, message, args) => {

    if (!hasDiscordPermission(message, PermissionFlagsBits.BanMembers, 'Ban Members')) return;

    if (!message.guild.members.me.permissions.has(PermissionFlagsBits.BanMembers)) {
      return embeds.deny(message, 'I need **Ban Members** permission to use this command');
    }

    if (!args[0]) {
      return runHelpCommand(message, 'ban');
    }

    let reason = args.slice(1).join(" ");
    if (!reason) reason = 'reasons';

    const userResult = client.findUser(message.guild, args[0]);

    if (userResult.found) {
      const user = userResult.user;

      if (!checkSelfAction(message, user, 'ban')) return;
      if (!checkBotAction(message, user)) return;
      if (!checkUserMemberHierarchy(message, user)) return;
      if (!checkBotMemberHierarchy(message, user)) return;
      if (!checkDiscordActionable(message, user, 'ban')) return;

      try {
        await user.ban({ reason: `${reason} | Banned by: ${message.author.tag}` });

        try {
          await addModerationCase(
            message.guild.id,
            user.user.id,
            message.author.id,
            'ban',
            reason
          );
        } catch (historyError) {

        }

        embeds.success(message, `**${user.user.tag}** has been **banned** for ${reason}`);
      } catch (error) {
        embeds.deny(message, 'An error occurred while trying to ban this user.');
      }
    } else {
      const userInput = args[0];

      if (!/^\d+$/.test(userInput)) {
        return embeds.deny(message, `I couldn't find a user matching: **${userInput}**. For hackban, please provide a valid user ID.`);
      }

      if (userInput === message.author.id) {
        return embeds.warn(message, "You can not ban yourself!");
      }

      if (userInput === message.guild.members.me.id) {
        return message.channel.send('leave me alone');
      }

      try {
        await message.guild.members.ban(userInput, { reason: `${reason} | Hackbanned by: ${message.author.tag}` });

        try {
          await addModerationCase(
            message.guild.id,
            userInput,
            message.author.id,
            'ban',
            reason
          );
        } catch (historyError) {

        }

        embeds.success(message, `User ID **${userInput}** has been **hackbanned** for ${reason}`);
      } catch (error) {
        if (error.code === 10013) {
          embeds.deny(message, `User ID **${userInput}** is not a valid Discord user.`);
        } else if (error.code === 10026) {
          embeds.deny(message, `User ID **${userInput}** is already banned.`);
        } else {
          embeds.deny(message, 'An error occurred while trying to hackban this user.');
        }
      }
    }
  }
}
