const { PermissionFlagsBits } = require("discord.js");
const { hasDiscordPermission, checkSelfAction, checkBotAction, checkUserMemberHierarchy, checkBotMemberHierarchy, checkDiscordActionable, checkBotRoleHierarchy } = require('../../../utils/permissions');
const { embeds } = require('../../../core/embeds');
const { runHelpCommand } = require('../../../utils/commandProcessor');
const { addModerationCase, getSavedUserRoles, removeSavedUserRoles } = require('../../../database/moderation');

module.exports = {
  name: "unjail",
  description: `unjails the specified member`,
  usage: '{guildprefix}unjail [user]\n{guildprefix}unjail [user] [reason]',
  permission: 'Manage Roles',
  run: async(client, message, args) => {

    // Permission checks first
    if (!hasDiscordPermission(message, PermissionFlagsBits.ManageRoles, 'Manage Roles')) return;

    if (!message.guild.members.me.permissions.has(PermissionFlagsBits.ManageRoles)) {
      return embeds.deny(message, 'I need **Manage Roles** permission to use this command');
    }

    // Check if user provided input
    if (!args[0]) {
      return runHelpCommand(message, 'unjail');
    }

    // Use universal user finder
    const userResult = client.findUser(message.guild, args[0]);

    if (!userResult.found) {
      return embeds.deny(message, userResult.error || 'User not found');
    }

    const user = userResult.user;
    let reason = args.slice(1).join(" ");
    if (!reason) reason = 'reasons';

    // Self-action check
    if (!checkSelfAction(message, user.user, 'unjail')) return;

    // Bot-action check
    if (!checkBotAction(message, user.user)) return;

    // Hierarchy checks
    if (!checkUserMemberHierarchy(message, user)) return;
    if (!checkBotMemberHierarchy(message, user)) return;

    // Check if Discord allows role management on this member
    if (!checkDiscordActionable(message, user, 'manage roles')) return;

    // Check if jail role exists
    const jailRole = message.guild.roles.cache.find(role => role.name === 'jailed');

    if (!jailRole) {
      return embeds.deny(message, `Jail role not found! Please run \`,jailsetup\` first to set up the jail system.`);
    }

    // Check if bot can manage the jail role
    if (!checkBotRoleHierarchy(message, jailRole)) return;

    // Check if user is actually jailed
    if (!user.roles.cache.has(jailRole.id)) {
      return embeds.warn(message, `**${user.user.username}** is not jailed`);
    }

    try {
      // Get saved roles from database
      const savedRoles = await getSavedUserRoles(message.guild.id, user.user.id);

      // Remove jail role and restore saved roles
      try {
        if (savedRoles && savedRoles.length > 0) {
          // Filter out roles that no longer exist in the guild
          const validRoles = savedRoles.filter(roleId => message.guild.roles.cache.has(roleId));

          // Set roles to the saved ones (this removes jail role and adds back the saved roles)
          await user.roles.set(validRoles, `Unjailed by ${message.author.tag}: ${reason}`);
        } else {
          // If no saved roles, just remove the jail role
          await user.roles.remove(jailRole.id, `Unjailed by ${message.author.tag}: ${reason}`);
        }
      } catch (roleError) {
        if (roleError.code === 50013) {
          return embeds.deny(message, `I don't have permission to manage **${user.user.username}**'s roles. Please check my role hierarchy and permissions.`);
        }
        throw roleError; // Re-throw if it's not a permission error
      }

      // Remove saved roles from database
      await removeSavedUserRoles(message.guild.id, user.user.id);

      // Add to moderation history
      try {
        await addModerationCase(
          message.guild.id,
          user.user.id,
          message.author.id,
          'unjail',
          reason
        );
      } catch (historyError) {
        // Don't fail the command if history logging fails
      }

      // Send success message
      embeds.success(message, `**${user.user.username}** has been **unjailed** for ${reason}`);

    } catch (error) {
      embeds.deny(message, `An error occurred while unjailing **${user.user.username}**`);
    }
  }
}
