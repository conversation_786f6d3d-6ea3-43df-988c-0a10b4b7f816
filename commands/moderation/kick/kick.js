const { PermissionFlagsBits } = require("discord.js");
const { embeds } = require('../../../core/embeds');
const { hasDiscordPermission, checkSelfAction, checkBotAction, checkUserMemberHierarchy, checkBotMemberHierarchy, checkDiscordActionable } = require('../../../utils/permissions');
const { runHelpCommand } = require('../../../utils/commandProcessor');
const { addModerationCase } = require('../../../database/moderation');

module.exports = {
  name: "kick",
  aliases: ['boot'],
  description: 'Kicks the mentioned user from the server',
  usage: '{guildprefix}kick [user]\n{guildprefix}kick [user] [reason]',
  permission: 'Kick Members',
  run: async(client, message, args) => {

    // Permission checks first
    if (!hasDiscordPermission(message, PermissionFlagsBits.KickMembers, 'Kick Members')) return;

    if (!message.guild.members.me.permissions.has(PermissionFlagsBits.KickMembers)) {
      return embeds.deny(message, 'I need **Kick Members** permission to use this command');
    }

    // Check if user provided input
    if (!args[0]) {
      return runHelpCommand(message, 'kick');
    }

    // Use universal user finder
    const userResult = client.findUser(message.guild, args[0]);

    if (!userResult.found) {
      return embeds.deny(message, userResult.error || 'User not found');
    }

    const user = userResult.user;
    let reason = args.slice(1).join(" ");
    if (!reason) reason = 'reasons';

    if (!checkSelfAction(message, user, 'kick')) return;
    if (!checkBotAction(message, user)) return;

    if (!checkUserMemberHierarchy(message, user)) return;
    if (!checkBotMemberHierarchy(message, user)) return;

    // Check if Discord allows the kick action
    if (!checkDiscordActionable(message, user, 'kick')) return;

    // Perform the kick
    try {
      await user.kick(`${reason} | Kicked by: ${message.author.tag}`);

      // Add to moderation history
      try {
        await addModerationCase(
          message.guild.id,
          user.user.id,
          message.author.id,
          'kick',
          reason
        );
      } catch (historyError) {
        // Don't fail the command if history logging fails
      }

      embeds.success(message, `**${user.user.tag}** has been **kicked** for ${reason}`);
    } catch (error) {
      embeds.deny(message, 'An error occurred while trying to kick this user.');
    }
  }
}
