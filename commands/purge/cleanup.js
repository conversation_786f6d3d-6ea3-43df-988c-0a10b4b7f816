const { PermissionFlagsBits } = require("discord.js");
const { embeds } = require('../../../core/embeds');
const { hasDiscordPermission } = require('../../../utils/permissions');
const { runHelpCommand } = require('../../../utils/commandProcessor');

module.exports = {
  name: "cleanup",
  aliases: ['bc'],
  description: `clean up bot messages and invoking commands in the channel`,
  usage: '{guildprefix}cleanup [number]',
  permission: 'Manage Messages',
  run: async(client, message, args) => {

    // Permission checks first
    if (!hasDiscordPermission(message, PermissionFlagsBits.ManageMessages, 'Manage Messages')) return;

    if (!message.guild.members.me.permissions.has(PermissionFlagsBits.ManageMessages)) {
      return embeds.deny(message, 'I need **Manage Messages** permission to use this command');
    }

    let number = 50;

    if (args[0]) {
      const inputNumber = parseInt(args[0]);

      if (isNaN(inputNumber) || inputNumber < 1) {
        return runHelpCommand(message, 'cleanup');
      }

      number = Math.min(inputNumber, 500);
    }

    try {
      // Fetch messages
      const messages = await message.channel.messages.fetch({ limit: number });

      const messagesToDelete = messages.filter(msg =>
        msg.author.bot || msg.id === message.id
      );

      if (messagesToDelete.size === 0) {
        const noMessagesMsg = await embeds.warn(message, `No bot messages or commands found in the last ${number} messages.`);

        setTimeout(() => {
          noMessagesMsg.delete().catch(() => {});
        }, 5000);

        return;
      }

      await message.channel.bulkDelete(messagesToDelete, true);
      const successMsg = await embeds.success(message, `Successfully cleaned up ${messagesToDelete.size} message${messagesToDelete.size === 1 ? '' : 's'}! ${number > 500 ? `(Capped at 500 from ${args[0]})` : ''}`);

      setTimeout(() => {
        successMsg.delete().catch(() => {});
      }, 5000);

    } catch (error) {
      if (error.code === 50034) {
        embeds.deny(message, 'Cannot delete messages **older than 14 days** due to Discord limitations.');
      } else {
        embeds.deny(message, 'An error occurred while trying to clean up messages.');
      }
    }
  }
}