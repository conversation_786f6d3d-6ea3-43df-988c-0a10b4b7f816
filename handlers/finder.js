/**
 * Universal finder utilities for Discord.js
 * Provides automatic closest match selection for roles, users, and channels
 */

/**
 * Universal role finder function with automatic closest match
 * @param {Guild} guild - The Discord guild
 * @param {string} roleInput - The role input (mention, ID, or name)
 * @returns {Object} - { found: boolean, role: Role|null, error: string|null }
 */
function findRole(guild, roleInput) {
  if (!guild || !roleInput) return { found: false, role: null, error: 'Invalid input' };

  // Try to find by mention first
  const mentionMatch = roleInput.match(/^<@&(\d+)>$/);
  if (mentionMatch) {
    const role = guild.roles.cache.get(mentionMatch[1]);
    return role ? { found: true, role } : { found: false, role: null, error: `Role with ID ${mentionMatch[1]} not found` };
  }

  // Try to find by ID
  let role = guild.roles.cache.get(roleInput);
  if (role) return { found: true, role };

  // Try to find by exact name (case-insensitive)
  role = guild.roles.cache.find(r => r.name.toLowerCase() === roleInput.toLowerCase());
  if (role) return { found: true, role };

  // Find closest match automatically
  const input = roleInput.toLowerCase();

  // Try partial matches (role name contains input or input contains role name)
  role = guild.roles.cache.find(r => {
    const roleName = r.name.toLowerCase();
    return roleName.includes(input) || input.includes(roleName);
  });
  if (role) return { found: true, role };

  // Try starting character matches (for inputs 3+ characters)
  if (input.length >= 3) {
    role = guild.roles.cache.find(r => r.name.toLowerCase().startsWith(input.substring(0, 3)));
    if (role) return { found: true, role };
  }

  return {
    found: false,
    role: null,
    error: `I was unable to find a role with the name: ${roleInput}`
  };
}

/**
 * Universal user finder function with automatic closest match and global ID fallback
 * @param {Guild} guild - The Discord guild
 * @param {string} userInput - The user input (mention, ID, username, or display name)
 * @param {Client} client - Discord client (optional, for global user ID search)
 * @returns {Promise<Object>} - { found: boolean, user: GuildMember|User|null, error: string|null, isGlobal: boolean }
 */
async function findUser(guild, userInput, client = null) {
  if (!userInput) return { found: false, user: null, error: 'Invalid input', isGlobal: false };
  if (!guild) return { found: false, user: null, error: 'Invalid guild', isGlobal: false };

  // Try to find by mention first
  const mentionMatch = userInput.match(/^<@!?(\d+)>$/);
  if (mentionMatch) {
    const user = guild.members.cache.get(mentionMatch[1]);
    if (user) return { found: true, user, isGlobal: false };

    // If not found in guild and client provided, try global fetch
    if (client) {
      try {
        const globalUser = await client.users.fetch(mentionMatch[1]);
        return { found: true, user: globalUser, isGlobal: true };
      } catch {
        // Continue to error below
      }
    }
    return { found: false, user: null, error: `User with ID ${mentionMatch[1]} not found`, isGlobal: false };
  }

  // Try to find by ID in guild first
  let user = guild.members.cache.get(userInput);
  if (user) return { found: true, user, isGlobal: false };

  // Try to find by exact username (case-insensitive)
  user = guild.members.cache.find(m => m.user.username.toLowerCase() === userInput.toLowerCase());
  if (user) return { found: true, user, isGlobal: false };

  // Try to find by exact display name (case-insensitive)
  user = guild.members.cache.find(m => m.displayName.toLowerCase() === userInput.toLowerCase());
  if (user) return { found: true, user, isGlobal: false };

  // Find closest match automatically
  const input = userInput.toLowerCase();

  // Try partial matches in username
  user = guild.members.cache.find(m => {
    const username = m.user.username.toLowerCase();
    return username.includes(input) || input.includes(username);
  });
  if (user) return { found: true, user, isGlobal: false };

  // Try partial matches in display name
  user = guild.members.cache.find(m => {
    const displayName = m.displayName.toLowerCase();
    return displayName.includes(input) || input.includes(displayName);
  });
  if (user) return { found: true, user, isGlobal: false };

  // Try starting character matches (for inputs 3+ characters)
  if (input.length >= 3) {
    const prefix = input.substring(0, 3);
    user = guild.members.cache.find(m =>
      m.user.username.toLowerCase().startsWith(prefix) ||
      m.displayName.toLowerCase().startsWith(prefix)
    );
    if (user) return { found: true, user, isGlobal: false };
  }

  // If not found in guild and input looks like user ID, try global fetch
  if (client && /^\d{17,19}$/.test(userInput)) {
    try {
      const globalUser = await client.users.fetch(userInput);
      return { found: true, user: globalUser, isGlobal: true };
    } catch {
      // User not found globally either
    }
  }

  return {
    found: false,
    user: null,
    error: `I was unable to find a user with the name: ${userInput}`,
    isGlobal: false
  };
}

/**
 * Universal channel finder function with automatic closest match
 * @param {Guild} guild - The Discord guild
 * @param {string} channelInput - The channel input (mention, ID, or name)
 * @returns {Object} - { found: boolean, channel: Channel|null, error: string|null }
 */
function findChannel(guild, channelInput) {
  if (!guild || !channelInput) return { found: false, channel: null, error: 'Invalid input' };

  // Try to find by mention first
  const mentionMatch = channelInput.match(/^<#(\d+)>$/);
  if (mentionMatch) {
    const channel = guild.channels.cache.get(mentionMatch[1]);
    return channel ? { found: true, channel } : { found: false, channel: null, error: `Channel with ID ${mentionMatch[1]} not found` };
  }

  // Try to find by ID
  let channel = guild.channels.cache.get(channelInput);
  if (channel) return { found: true, channel };

  // Try to find by exact name (case-insensitive)
  channel = guild.channels.cache.find(ch => ch.name.toLowerCase() === channelInput.toLowerCase());
  if (channel) return { found: true, channel };

  // Find closest match automatically
  const input = channelInput.toLowerCase();

  // Try partial matches (channel name contains input or input contains channel name)
  channel = guild.channels.cache.find(ch => {
    const channelName = ch.name.toLowerCase();
    return channelName.includes(input) || input.includes(channelName);
  });
  if (channel) return { found: true, channel };

  // Try starting character matches (for inputs 3+ characters)
  if (input.length >= 3) {
    channel = guild.channels.cache.find(ch => ch.name.toLowerCase().startsWith(input.substring(0, 3)));
    if (channel) return { found: true, channel };
  }

  return {
    found: false,
    channel: null,
    error: `I was unable to find a channel with the name: ${channelInput}`
  };
}

module.exports = {
  findRole,
  findUser,
  findChannel
};
