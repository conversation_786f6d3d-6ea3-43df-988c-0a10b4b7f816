// Message handler for command processing, autoreact, and autoresponder

module.exports = {
  name: 'messageCreate',
  async execute(message) {
    // Skip bots
    if (message.author.bot) return;

    // Skip if no guild
    if (!message.guild) return;

    // Process autoreacts first
    await processAutoreact(message);

    // Process autoresponders second
    await processAutoresponder(message);

    // Process commands last
    await processCommand(message);
  },
};

// AFK system temporarily disabled - missing database schema

async function processAutoreact(message) {
  try {
    // Skip if message is empty or only whitespace
    if (!message.content || !message.content.trim()) return;

    const { Autoreact } = require('../../database');

    // Get all autoreacts for this guild
    const autoreacts = await Autoreact.find({ GuildID: message.guild.id });

    if (!autoreacts || autoreacts.length === 0) return;

    const messageContent = message.content.toLowerCase();

    // Check each autoreact trigger
    for (const autoreact of autoreacts) {
      // Check if the message contains the trigger word
      if (messageContent.includes(autoreact.Word.toLowerCase())) {
        try {
          let emojiToReact;

          if (autoreact.IsCustom) {
            // For custom emojis, check if it still exists in the guild
            const emoji = message.guild.emojis.cache.get(autoreact.EmojiID);
            if (emoji) {
              emojiToReact = emoji;
            } else {
              // Remove invalid emoji from database
              await Autoreact.findOneAndRemove({ _id: autoreact._id });
              continue;
            }
          } else {
            // For unicode emojis
            emojiToReact = autoreact.EmojiName;
          }

          // React to the message
          await message.react(emojiToReact);
        } catch (error) {
          // Silent fail for individual reactions
          // Could be due to missing permissions, invalid emoji, etc.
        }
      }
    }
  } catch (error) {
    // Silent fail for autoreact system
  }
}

async function processAutoresponder(message) {
  try {
    // Skip if message is empty or only whitespace
    if (!message.content || !message.content.trim()) return;

    const { Autoresponder } = require('../../database');

    // Get all autoresponders for this guild
    const autoresponders = await Autoresponder.find({ GuildID: message.guild.id });

    if (!autoresponders || autoresponders.length === 0) return;

    const messageContent = message.content;

    // Check each autoresponder trigger
    for (const autoresponder of autoresponders) {
      let triggered = false;

      if (autoresponder.Strict) {
        // Strict mode: exact word match (case insensitive)
        const words = messageContent.toLowerCase().split(/\s+/);
        triggered = words.includes(autoresponder.Trigger.toLowerCase());
      } else {
        // Non-strict mode: contains trigger anywhere in message (case insensitive)
        triggered = messageContent.toLowerCase().includes(autoresponder.Trigger.toLowerCase());
      }

      if (triggered) {
        try {
          // Delete original message if delete option is enabled
          if (autoresponder.Delete) {
            try {
              await message.delete();
            } catch (error) {
              // Silent fail if can't delete (missing permissions, etc.)
            }
          }

          // Send response
          let responseMessage;
          if (autoresponder.Reply) {
            responseMessage = await message.reply(autoresponder.Response);
          } else {
            responseMessage = await message.channel.send(autoresponder.Response);
          }

          // Self-destruct if enabled
          if (autoresponder.SelfDestruct > 0) {
            setTimeout(async () => {
              try {
                await responseMessage.delete();
              } catch (error) {
                // Silent fail if message was already deleted
              }
            }, autoresponder.SelfDestruct * 1000);
          }

          // Only trigger one autoresponder per message
          break;

        } catch (error) {
          // Silent fail for individual autoresponder
        }
      }
    }
  } catch (error) {
    // Silent fail for autoresponder system
  }
}

async function processCommand(message) {
  try {
    // Process commands with new prefix system
    const { processCommand } = require('../../utils/commandProcessor');
    await processCommand(message, false);
  } catch (error) {
    // Silent fail for commands
  }
}


