const { ensureGuildData } = require('../../database/global');
const { EmbedVariableProcessor } = require('../../utils/embedbuilder');
const { ActivityType } = require('discord.js');

module.exports = {
  name: 'presenceUpdate',
  async execute(oldPresence, newPresence, client) {
    // Skip if no presence or guild
    if (!newPresence || !newPresence.guild || !newPresence.member) return;

    // Skip bots
    if (newPresence.member.user.bot) return;

    // Skip if invisible or offline
    if (newPresence?.status === "invisible" || newPresence?.status === "offline" ||
        oldPresence?.status === "invisible" || oldPresence?.status === "offline") return;

    try {
      await processVanityStatusDetection(newPresence);
    } catch (error) {
      // Silent fail for vanity detection
    }
  },
};

async function processVanityStatusDetection(newPresence) {
  // Get guild data to check for vanity configuration
  const guildData = await ensureGuildData(newPresence.guild.id);

  // Check if vanity is configured
  if (!guildData.Vanity || !guildData.VanityRoles || guildData.VanityRoles.length === 0) {
    return; // No vanity configured
  }

  const member = newPresence.member;
  const vanityText = guildData.Vanity;
  const vanityRoles = guildData.VanityRoles;

  // Check if user has vanity in their custom status
  let hasVanityInStatus = false;

  if (newPresence.activities && newPresence.activities[0]?.type === ActivityType.Custom) {
    const customStatus = newPresence.activities[0]?.state;
    if (customStatus) {
      // Check if vanity text is anywhere in the custom status
      hasVanityInStatus = customStatus.toLowerCase().includes(vanityText.toLowerCase());
    }
  }

  // Check if member currently has any vanity roles
  const hasVanityRole = member.roles.cache.hasAny(...vanityRoles);

  if (hasVanityInStatus && !hasVanityRole) {
    // Add vanity roles
    try {
      await member.roles.add(vanityRoles, 'added vanity role(s)');
      console.log(`[VANITY] Added roles to ${member.user.tag} for having vanity in status`);

      // Send vanity message
      if (guildData.VanityMessage && guildData.VanityLogChannel) {
        const logChannel = newPresence.guild.channels.cache.get(guildData.VanityLogChannel);
        if (logChannel) {
          try {
            EmbedVariableProcessor.process(logChannel, guildData.VanityMessage, {
              user: member.user,
              guild: newPresence.guild,
              channel: logChannel,
              vanity: guildData.Vanity,
              roles: vanityRoles.map(roleId => {
                const role = newPresence.guild.roles.cache.get(roleId);
                return role ? role.name : 'Unknown Role';
              }).join(', '),
              source: 'custom status'
            });
          } catch (error) {
            // Fallback message
            logChannel.send(`${member.user} has vanity in their status`);
          }
        }
      }
    } catch (error) {
      console.error(`[VANITY] Failed to add roles:`, error.message);
    }

  } else if (!hasVanityInStatus && hasVanityRole) {
    // Remove vanity roles silently (no notification)
    try {
      await member.roles.remove(vanityRoles, 'removed vanity role(s)');
      console.log(`[VANITY] Removed roles from ${member.user.tag} for removing vanity from status`);
    } catch (error) {
      console.error(`[VANITY] Failed to remove roles:`, error.message);
    }
  }
}
