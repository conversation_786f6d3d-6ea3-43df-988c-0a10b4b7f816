const { <PERSON>nick } = require('../../database');

module.exports = {
  name: 'guildMemberUpdate',
  async execute(oldMember, newMember, client) {
    try {
      // Check if nickname changed
      if (oldMember.nickname !== newMember.nickname) {
        // Check if this user has a forced nickname
        const forcedNick = await Forcenick.findOne({
          GuildID: newMember.guild.id,
          UserID: newMember.user.id
        });

        if (forcedNick) {
          // If the new nickname doesn't match the forced nickname, restore it
          if (newMember.nickname !== forcedNick.ForcedNickname) {
            try {
              await newMember.setNickname(forcedNick.ForcedNickname, 'Enforcing forced nickname');
              
            } catch (error) {
              // If we can't set the nickname (permissions, hierarchy, etc.), remove the forced nickname
              await Forcenick.findOneAndDelete({
                GuildID: newMember.guild.id,
                UserID: newMember.user.id
              });
            }
          }
        }
      }
    } catch (error) {
      // Silent error handling
    }
  },
};
