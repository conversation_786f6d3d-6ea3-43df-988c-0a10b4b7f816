const { ensureGuildData } = require('../../database/global');
const { EmbedVariableProcessor } = require('../../utils/embedbuilder');
const { Autorole } = require('../../database');

module.exports = {
  name: 'guildMemberAdd',
  async execute(member, client) {
    try {
      // Skip bots - they don't get welcome messages or autoroles
      if (member.user.bot) {
        return;
      }

      // Process autoroles first
      await processAutoroles(member);

      // Get guild data to check for welcome message configuration
      const guildData = await ensureGuildData(member.guild.id);
      
      // Check if welcome message is configured
      if (!guildData.WelcomeChannel || !guildData.WelcomeMessage) {
        return; // No welcome message configured
      }
      
      // Get the welcome channel
      const welcomeChannel = member.guild.channels.cache.get(guildData.WelcomeChannel);
      if (!welcomeChannel) {
        return;
      }
      
      // Process and send the welcome message with variables
      try {
        EmbedVariableProcessor.process(welcomeChannel, guildData.WelcomeMessage, {
          user: member.user,
          guild: member.guild,
          channel: welcomeChannel
        });
      } catch (error) {

        // Fallback to simple text message
        welcomeChannel.send(`Welcome ${member}!`);
      }
      
    } catch (error) {
      // Silent fail for join event
    }
  },
};

async function processAutoroles(member) {
  try {
    // Get autorole data for this guild
    const autoroleData = await Autorole.findOne({ GuildID: member.guild.id });

    if (!autoroleData || !autoroleData.RoleIDs || autoroleData.RoleIDs.length === 0) {
      return; // No autoroles configured
    }

    // Get valid roles that still exist in the guild
    const validRoles = [];
    const invalidRoles = [];

    for (const roleId of autoroleData.RoleIDs) {
      const role = member.guild.roles.cache.get(roleId);
      if (role) {
        // Check if bot can assign this role (hierarchy check)
        if (member.guild.members.me.roles.highest.position > role.position) {
          validRoles.push(role);
        }
      } else {
        invalidRoles.push(roleId);
      }
    }

    // Clean up invalid roles from database
    if (invalidRoles.length > 0) {
      autoroleData.RoleIDs = autoroleData.RoleIDs.filter(roleId => !invalidRoles.includes(roleId));

      if (autoroleData.RoleIDs.length === 0) {
        await Autorole.findOneAndRemove({ GuildID: member.guild.id });
      } else {
        await autoroleData.save();
      }
    }

    // Assign valid roles to the member
    if (validRoles.length > 0) {
      try {
        await member.roles.add(validRoles, 'Autorole assignment');
      } catch (error) {
        // Silent fail for role assignment - could be due to permissions, role hierarchy, etc.
      }
    }

  } catch (error) {
    // Silent fail for autorole system
  }
}
