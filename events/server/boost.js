const { ensureGuildData } = require('../../database/global');
const { EmbedVariableProcessor } = require('../../utils/embedbuilder');

module.exports = {
  name: 'guildMemberUpdate',
  async execute(oldMember, newMember, client) {
    try {
      // Check if this is a boost event (premium since timestamp changed)
      const wasBooster = oldMember.premiumSince !== null;
      const isBooster = newMember.premiumSince !== null;
      
      // Only trigger on new boosts (not when boost is removed)
      if (!wasBooster && isBooster) {
        // Get guild data to check for boost message configuration
        const guildData = await ensureGuildData(newMember.guild.id);
        
        // Check if boost message is configured
        if (!guildData.BoostChannel || !guildData.BoostMessage) {
          return; // No boost message configured
        }
        
        // Get the boost channel
        const boostChannel = newMember.guild.channels.cache.get(guildData.BoostChannel);
        if (!boostChannel) {

          return;
        }
        
        // Process and send the boost message with variables
        try {
          EmbedVariableProcessor.process(boostChannel, guildData.BoostMessage, {
            user: newMember.user,
            guild: newMember.guild,
            channel: boostChannel
          });
          

        } catch (error) {

          // Fallback to simple text message
          boostChannel.send(`Thank you ${newMember} for boosting the server! 🚀`);
        }
      }
      
    } catch (error) {

    }
  },
};
