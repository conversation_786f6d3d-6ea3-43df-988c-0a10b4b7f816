const { ensureGuildData } = require('../../database/global');
const { EmbedVariableProcessor } = require('../../utils/embedbuilder');

module.exports = {
  name: 'guildMemberRemove',
  async execute(member, client) {
    try {
      // Get guild data to check for leave message configuration
      const guildData = await ensureGuildData(member.guild.id);

      // Check if leave message is configured
      if (!guildData.LeaveChannel || !guildData.LeaveMessage) {
        return; // No leave message configured
      }

      // Get the leave channel
      const leaveChannel = member.guild.channels.cache.get(guildData.LeaveChannel);
      if (!leaveChannel) {
        return;
      }

      // Process and send the leave message with variables
      try {
        EmbedVariableProcessor.process(leaveChannel, guildData.LeaveMessage, {
          user: member.user,
          guild: member.guild,
          channel: leaveChannel
        });
      } catch (error) {

        // Fallback to simple text message
        leaveChannel.send(`Goodbye ${member.user.username}!`);
      }
      
    } catch (error) {

    }
  },
};
