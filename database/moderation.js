const Moderation = require('./models/moderation');

/**
 * Add a new moderation case to the database
 * @param {string} guildId - Guild ID
 * @param {string} userId - User ID being moderated
 * @param {string} moderatorId - Moderator ID
 * @param {string} action - Moderation action (warn, mute, kick, ban, etc.)
 * @param {string} reason - Reason for the action
 * @param {string} duration - Duration (optional, for timed actions)
 * @returns {Promise<Object>} The created moderation case
 */
async function addModerationCase(guildId, userId, moderatorId, action, reason, duration = null) {
  try {
    // Get the next case ID for this guild
    const lastCase = await Moderation.findOne({ guildId }).sort({ caseId: -1 });
    const nextCaseId = lastCase ? lastCase.caseId + 1 : 1;

    const moderationCase = new Moderation({
      guildId,
      caseId: nextCaseId,
      userId,
      moderatorId,
      action,
      reason,
      duration
    });

    await moderationCase.save();
    return moderationCase;
  } catch (error) {
    throw error;
  }
}

/**
 * Get moderation history for a user
 * @param {string} guildId - Guild ID
 * @param {string} userId - User ID
 * @param {string} action - Optional action filter
 * @returns {Promise<Array>} Array of moderation cases
 */
async function getModerationHistory(guildId, userId, action = null) {
  try {
    const query = { guildId, userId };
    if (action) {
      query.action = action;
    }
    
    return await Moderation.find(query).sort({ timestamp: -1 });
  } catch (error) {
    throw error;
  }
}

/**
 * Get a specific moderation case by case ID
 * @param {string} guildId - Guild ID
 * @param {number} caseId - Case ID
 * @returns {Promise<Object|null>} The moderation case or null if not found
 */
async function getModerationCase(guildId, caseId) {
  try {
    return await Moderation.findOne({ guildId, caseId });
  } catch (error) {
    throw error;
  }
}

/**
 * Get moderation history by action type
 * @param {string} guildId - Guild ID
 * @param {string} userId - User ID
 * @param {string} action - Action type
 * @returns {Promise<Array>} Array of moderation cases
 */
async function getModerationHistoryByAction(guildId, userId, action) {
  try {
    return await Moderation.find({ guildId, userId, action }).sort({ timestamp: -1 });
  } catch (error) {
    throw error;
  }
}

/**
 * Remove a specific moderation case
 * @param {string} guildId - Guild ID
 * @param {number} caseId - Case ID
 * @returns {Promise<Object>} Deletion result
 */
async function removeModerationCase(guildId, caseId) {
  try {
    return await Moderation.deleteOne({ guildId, caseId });
  } catch (error) {
    throw error;
  }
}

/**
 * Remove all moderation cases for a user
 * @param {string} guildId - Guild ID
 * @param {string} userId - User ID
 * @returns {Promise<Object>} Deletion result
 */
async function removeAllModerationCases(guildId, userId) {
  try {
    return await Moderation.deleteMany({ guildId, userId });
  } catch (error) {
    throw error;
  }
}

/**
 * Save user roles (for jail system)
 * @param {string} guildId - Guild ID
 * @param {string} userId - User ID
 * @param {Array} roles - Array of role IDs
 * @returns {Promise<void>}
 */
async function saveUserRoles(guildId, userId, roles) {
  // This would typically be stored in a separate collection
  // For now, we'll add it to the moderation case as metadata
  // In a full implementation, you might want a separate UserRoles model
  try {
    // Store roles as a special moderation case type
    const moderationCase = new Moderation({
      guildId,
      caseId: 0, // Special case ID for role storage
      userId,
      moderatorId: 'system',
      action: 'role_save',
      reason: JSON.stringify(roles),
      duration: null
    });
    
    // Remove any existing role saves for this user
    await Moderation.deleteMany({ guildId, userId, action: 'role_save' });
    
    await moderationCase.save();
  } catch (error) {
    throw error;
  }
}

/**
 * Get saved user roles (for jail system)
 * @param {string} guildId - Guild ID
 * @param {string} userId - User ID
 * @returns {Promise<Array>} Array of role IDs
 */
async function getSavedUserRoles(guildId, userId) {
  try {
    const roleCase = await Moderation.findOne({ 
      guildId, 
      userId, 
      action: 'role_save' 
    });
    
    if (roleCase) {
      return JSON.parse(roleCase.reason);
    }
    
    return [];
  } catch (error) {
    throw error;
  }
}

/**
 * Remove saved user roles (for jail system)
 * @param {string} guildId - Guild ID
 * @param {string} userId - User ID
 * @returns {Promise<void>}
 */
async function removeSavedUserRoles(guildId, userId) {
  try {
    await Moderation.deleteMany({ guildId, userId, action: 'role_save' });
  } catch (error) {
    throw error;
  }
}

module.exports = {
  addModerationCase,
  getModerationHistory,
  getModerationCase,
  getModerationHistoryByAction,
  removeModerationCase,
  removeAllModerationCases,
  saveUserRoles,
  getSavedUserRoles,
  removeSavedUserRoles
};
