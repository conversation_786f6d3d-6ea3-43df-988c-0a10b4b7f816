const connection = require('./connection');

// Import models
const User = require('./models/user');
const Guild = require('./models/guild');
const Blacklist = require('./models/blacklist');
const Moderation = require('./models/moderation');
const Autoreact = require('./models/autoreact');
const Autorole = require('./models/autorole');
const Autoresponder = require('./models/autoresponder');
const Forcenick = require('./models/forcenick');

/**
 * Initialize database connection
 * @param {string} connectionString - MongoDB connection string
 * @returns {Promise<boolean>} Connection success status
 */
async function initializeDatabase(connectionString) {
  return await connection.connect(connectionString);
}

/**
 * Check if database is ready
 * @returns {boolean} Connection status
 */
function isDatabaseReady() {
  return connection.isReady();
}

/**
 * Disconnect from database
 * @returns {Promise<void>}
 */
async function disconnectDatabase() {
  return await connection.disconnect();
}

module.exports = {
  // Connection functions
  initializeDatabase,
  isDatabaseReady,
  disconnectDatabase,

  // Models
  User,
  Guild,
  Blacklist,
  Moderation,
  Autoreact,
  Autorole,
  Autoresponder,
  Forcenick
};
