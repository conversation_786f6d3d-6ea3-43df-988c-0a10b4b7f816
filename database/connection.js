const mongoose = require('mongoose');


class DatabaseConnection {
  constructor() {
    this.isConnected = false;
    this.listenersSetup = false;
  }

  /**
   * Initialize database connection
   * @param {string} connectionString - MongoDB connection string
   * @returns {Promise<boolean>} Connection success status
   */
  async connect(connectionString) {
    if (this.isConnected) {

      return true;
    }

    if (!connectionString || connectionString === "false") {

      return false;
    }

    try {
      // Set strictQuery to false to suppress deprecation warning
      mongoose.set('strictQuery', false);

      await mongoose.connect(connectionString, {
        useUnifiedTopology: true,
        maxPoolSize: 10,
        serverSelectionTimeoutMS: 5000,
        socketTimeoutMS: 45000
      });

      this.isConnected = true;

      // Set up connection event listeners only once
      if (!this.listenersSetup) {
        this.setupEventListeners();
        this.listenersSetup = true;
      }
      
      return true;
    } catch (error) {

      return false;
    }
  }

  /**
   * Set up mongoose connection event listeners
   */
  setupEventListeners() {
    mongoose.connection.on('connected', () => {
      // Connected to MongoDB
    });

    mongoose.connection.on('error', (error) => {

    });

    mongoose.connection.on('disconnected', () => {
      this.isConnected = false;
    });

    mongoose.connection.on('reconnected', () => {
      this.isConnected = true;
    });

    // Handle application termination
    process.on('SIGINT', async () => {
      await this.disconnect();
      process.exit(0);
    });
  }

  /**
   * Disconnect from database
   * @returns {Promise<void>}
   */
  async disconnect() {
    if (!this.isConnected) {
      return;
    }

    try {
      await mongoose.connection.close();
      this.isConnected = false;
    } catch (error) {

    }
  }

  /**
   * Check if database is connected
   * @returns {boolean} Connection status
   */
  isReady() {
    return this.isConnected && mongoose.connection.readyState === 1;
  }
}

// Export singleton instance
module.exports = new DatabaseConnection();
