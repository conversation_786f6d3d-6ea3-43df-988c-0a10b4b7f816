// Compatibility layer for old global schema
// This redirects to the new Guild model for backward compatibility

const Guild = require('./models/guild');

/**
 * Ensures guild data exists in the database with default values
 * @param {string} guildId - The Discord guild ID
 * @returns {Promise<Object>} The guild data document
 */
async function ensureGuildData(guildId) {
  try {
    // Use findOneAndUpdate with upsert to avoid race conditions
    const data = await Guild.findOneAndUpdate(
      { GuildID: guildId },
      {
        $setOnInsert: {
          GuildID: guildId,
          Prefix: ','
        }
      },
      {
        upsert: true,
        new: true,
        setDefaultsOnInsert: true
      }
    );

    return data;
  } catch (error) {
    // If there's still a duplicate key error, just find and return the existing record
    if (error.code === 11000) {
      return await Guild.findOne({ GuildID: guildId });
    }
    throw error;
  }
}

// Export the Guild model as the global schema for backward compatibility
// and also export the ensureGuildData function
module.exports = Guild;
module.exports.ensureGuildData = ensureGuildData;