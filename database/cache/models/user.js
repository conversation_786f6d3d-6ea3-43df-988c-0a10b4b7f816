const BaseCache = require('../base');

class User {
  constructor() {
    // Automatic cache for user data - only cache what we need
    this.cache = new BaseCache({
      ttl: 30 * 60 * 1000, // 30 minutes TTL for LastFM data
      expiredTtl: 24 * 60 * 60 * 1000, // 24 hours expired
      modelName: 'user', // Database model name
      keyField: 'UserID', // Primary key field
      fieldsToCache: ['LastFMUsername'] // Only cache LastFM username
    });
  }

  // Get LastFM username - automatic cache handling
  async getLastFMUsername(userId) {
    return await this.cache.getField(userId, 'LastFMUsername');
  }

  // Set LastFM username - automatic database and cache update
  async setLast<PERSON>Username(userId, lastfmUsername) {
    return await this.cache.setField(userId, 'LastFMUsername', lastfmUsername);
  }

  // Utility methods
  invalidateUser(userId) {
    this.cache.invalidate(userId);
  }

  clearCache() {
    this.cache.clear();
  }

  getStats() {
    return {
      users: this.cache.getStats()
    };
  }
}

// Export singleton instance
module.exports = new User();
