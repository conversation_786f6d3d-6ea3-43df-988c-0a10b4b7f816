const BaseCache = require('../base');

class Blacklist {
  constructor() {
    // Create separate cache instances for users and servers
    this.userCache = new BaseCache({
      ttl: 60 * 60 * 1000, // 1 hour for user blacklist
      expiredTtl: 24 * 60 * 60 * 1000 // 24 hours expired
    });
    
    this.serverCache = new BaseCache({
      ttl: 60 * 60 * 1000, // 1 hour for server blacklist  
      expiredTtl: 24 * 60 * 60 * 1000 // 24 hours expired
    });
  }

  // Get user blacklist status from cache or database
  async getUserBlacklist(userId) {
    const cached = this.userCache.get(userId);
    
    // Check if we have active (fresh) cache
    if (cached && this.userCache.hasActive(userId)) {
      return cached.Blacklisted;
    }
    
    // Check if we have expired cache (fallback while fetching new data)
    if (cached && this.userCache.hasExpired(userId)) {
      // Use expired data as fallback, but fetch fresh data in background
      this.refreshUserData(userId); // Non-blocking refresh
      return cached.Blacklisted;
    }

    // No cache or cache too old - fetch from database
    return await this.fetchUserFromDatabase(userId);
  }

  // Fetch user data from database and cache it
  async fetchUserFromDatabase(userId) {
    // Check if database is connected
    if (!this.userCache.isDatabaseReady()) {
      return false; // Return false if database not ready
    }

    try {
      const User = require('../../models/user');
      const userData = await User.findOne({ UserID: userId });
      
      // Cache the result with database structure
      const cacheData = {
        UserID: userId,
        Blacklisted: userData ? userData.Blacklisted : false
      };
      
      this.userCache.set(userId, cacheData);
      return cacheData.Blacklisted;
    } catch (error) {
      console.error('Error fetching user blacklist:', error);
      return false; // Allow command execution if there's an error
    }
  }

  // Background refresh for expired user data
  async refreshUserData(userId) {
    try {
      await this.fetchUserFromDatabase(userId);
    } catch (error) {
      console.error('Error refreshing user data:', error);
    }
  }

  // Get server blacklist status from cache or database
  async getServerBlacklist(serverId) {
    const cached = this.serverCache.get(serverId);
    
    // Check if we have active (fresh) cache
    if (cached && this.serverCache.hasActive(serverId)) {
      return !!cached.ServerID; // If ServerID exists, it's blacklisted
    }
    
    // Check if we have expired cache (fallback while fetching new data)
    if (cached && this.serverCache.hasExpired(serverId)) {
      // Use expired data as fallback, but fetch fresh data in background
      this.refreshServerData(serverId); // Non-blocking refresh
      return !!cached.ServerID;
    }

    // No cache or cache too old - fetch from database
    return await this.fetchServerFromDatabase(serverId);
  }

  // Fetch server data from database and cache it
  async fetchServerFromDatabase(serverId) {
    // Check if database is connected
    if (!this.serverCache.isDatabaseReady()) {
      return false; // Return false if database not ready
    }

    try {
      const BlacklistModel = require('../../models/blacklist');
      const blacklistEntry = await BlacklistModel.findOne({ ServerID: serverId });
      
      // Cache the result with database structure (null if not blacklisted)
      const cacheData = blacklistEntry ? {
        ServerID: blacklistEntry.ServerID,
        Reason: blacklistEntry.Reason,
        BlacklistedBy: blacklistEntry.BlacklistedBy
      } : {
        ServerID: null // null means not blacklisted
      };
      
      this.serverCache.set(serverId, cacheData);
      return !!blacklistEntry;
    } catch (error) {
      console.error('Error fetching server blacklist:', error);
      return false; // Allow if there's an error
    }
  }

  // Background refresh for expired server data
  async refreshServerData(serverId) {
    try {
      await this.fetchServerFromDatabase(serverId);
    } catch (error) {
      console.error('Error refreshing server data:', error);
    }
  }

  // Update user blacklist in cache and database
  async setUserBlacklist(userId, blacklisted) {
    // Check if database is connected
    if (!this.userCache.isDatabaseReady()) {
      return false; // Can't set blacklist if database not ready
    }

    try {
      const User = require('../../models/user');

      // Use findOneAndUpdate with upsert to avoid race conditions
      await User.findOneAndUpdate(
        { UserID: userId },
        {
          $set: { Blacklisted: blacklisted },
          $setOnInsert: { UserID: userId }
        },
        {
          upsert: true,
          new: true
        }
      );

      // Update cache with database structure
      const cacheData = {
        UserID: userId,
        Blacklisted: blacklisted
      };
      
      this.userCache.set(userId, cacheData);
      return true;
    } catch (error) {
      console.error('Error setting user blacklist:', error);
      return false;
    }
  }

  // Update server blacklist in cache and database
  async setServerBlacklist(serverId, blacklisted, reason = 'reasons', blacklistedBy = null) {
    // Check if database is connected
    if (!this.serverCache.isDatabaseReady()) {
      return false; // Can't set blacklist if database not ready
    }

    try {
      const BlacklistModel = require('../../models/blacklist');

      if (blacklisted) {
        // Add to blacklist
        const blacklistEntry = new BlacklistModel({
          ServerID: serverId,
          Reason: reason,
          BlacklistedBy: blacklistedBy
        });
        await blacklistEntry.save();
        
        // Update cache with database structure
        const cacheData = {
          ServerID: serverId,
          Reason: reason,
          BlacklistedBy: blacklistedBy
        };
        this.serverCache.set(serverId, cacheData);
      } else {
        // Remove from blacklist
        await BlacklistModel.deleteOne({ ServerID: serverId });
        
        // Update cache to show not blacklisted
        const cacheData = {
          ServerID: null // null means not blacklisted
        };
        this.serverCache.set(serverId, cacheData);
      }

      return true;
    } catch (error) {
      console.error('Error setting server blacklist:', error);
      return false;
    }
  }

  // Invalidate user cache
  invalidateUser(userId) {
    this.userCache.invalidate(userId);
  }

  // Invalidate server cache
  invalidateServer(serverId) {
    this.serverCache.invalidate(serverId);
  }

  // Clear all cache
  clearCache() {
    this.userCache.clear();
    this.serverCache.clear();
  }

  // Get cache stats
  getStats() {
    const userStats = this.userCache.getStats();
    const serverStats = this.serverCache.getStats();
    
    return {
      users: userStats,
      servers: serverStats
    };
  }
}

// Export singleton instance
module.exports = new Blacklist();
