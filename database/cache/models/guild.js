const BaseCache = require('../base');

class Guild {
  constructor() {
    // Mirror database structure but cache only what we need
    // Currently nothing to cache - prefix is handled in prefix.js
    // Future: moderation settings, welcome/leave messages, etc.
    this.guildCache = new BaseCache({
      ttl: 60 * 60 * 1000, // 1 hour TTL for active data
      expiredTtl: 24 * 60 * 60 * 1000 // 24 hours TTL for expired data
    });
  }

  // Placeholder for future guild settings caching
  // When we add moderation settings, welcome messages, etc., they'll go here

  // Example method for future use:
  // async getGuildSettings(guildId) {
  //   const cached = this.guildCache.get(guildId);
  //   
  //   if (cached && this.guildCache.hasActive(guildId)) {
  //     return cached.settings;
  //   }
  //   
  //   if (cached && this.guildCache.hasExpired(guildId)) {
  //     this.refreshGuildData(guildId);
  //     return cached.settings;
  //   }
  //   
  //   return await this.fetchGuildFromDatabase(guildId);
  // }

  // Invalidate guild cache
  invalidateGuild(guildId) {
    this.guildCache.invalidate(guildId);
  }

  // Clear all cache
  clearCache() {
    this.guildCache.clear();
  }

  // Get cache stats
  getStats() {
    return {
      guilds: this.guildCache.getStats()
    };
  }
}

// Export singleton instance
module.exports = new Guild();
