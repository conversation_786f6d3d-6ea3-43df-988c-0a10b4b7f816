const BaseCache = require('../base');

class Prefix {
  constructor() {
    // Automatic cache for guild prefixes
    this.guildCache = new BaseCache({
      ttl: 30 * 60 * 1000, // 30 minutes TTL
      expiredTtl: 24 * 60 * 60 * 1000, // 24 hours expired
      modelName: 'guild', // Database model name
      keyField: 'GuildID', // Primary key field
      fieldsToCache: ['Prefix'] // Only cache prefix
    });

    // Automatic cache for user self prefixes
    this.userCache = new BaseCache({
      ttl: 30 * 60 * 1000, // 30 minutes TTL
      expiredTtl: 24 * 60 * 60 * 1000, // 24 hours expired
      modelName: 'user', // Database model name
      keyField: 'UserID', // Primary key field
      fieldsToCache: ['SelfPrefix'] // Only cache self prefix
    });
  }

  // Get guild prefix - automatic cache handling with default fallback
  async getGuildPrefix(guildId) {
    const prefix = await this.guildCache.getField(guildId, 'Prefix');
    return prefix || ','; // Default prefix if null/undefined
  }

  // Get user self prefix - automatic cache handling
  async getUserPrefix(userId) {
    return await this.userCache.getField(userId, 'SelfPrefix');
  }

  // Set guild prefix - automatic database and cache update
  async setGuildPrefix(guildId, prefix) {
    return await this.guildCache.setField(guildId, 'Prefix', prefix);
  }

  // Set user self prefix - automatic database and cache update
  async setUserPrefix(userId, prefix) {
    return await this.userCache.setField(userId, 'SelfPrefix', prefix);
  }

  // Clear user self prefix - set to null
  async clearUserPrefix(userId) {
    return await this.userCache.setField(userId, 'SelfPrefix', null);
  }



  // Check if message starts with any valid prefix for the user/guild
  async checkPrefixes(message) {
    const guildPrefix = await this.getGuildPrefix(message.guild.id);
    const userPrefix = await this.getUserPrefix(message.author.id);

    const content = message.content;

    // Universal prefix (bot mention - works everywhere)
    const botMention = `<@${message.client.user.id}>`;
    const botMentionNick = `<@!${message.client.user.id}>`;

    // Check self prefix first (highest priority - works everywhere)
    if (userPrefix && content.startsWith(userPrefix)) {
      return {
        prefix: userPrefix,
        type: 'self'
      };
    }

    // Check universal prefix (bot mention - works everywhere)
    if (content.startsWith(botMention)) {
      return {
        prefix: botMention,
        type: 'universal'
      };
    }

    if (content.startsWith(botMentionNick)) {
      return {
        prefix: botMentionNick,
        type: 'universal'
      };
    }

    // Check server prefix (if set, overrides default)
    if (guildPrefix !== ',' && content.startsWith(guildPrefix)) {
      return {
        prefix: guildPrefix,
        type: 'server'
      };
    }

    // Check default prefix (fallback when no server prefix set)
    if (guildPrefix === ',' && content.startsWith(',')) {
      return {
        prefix: ',',
        type: 'default'
      };
    }

    return null;
  }

  // Utility methods
  invalidateGuild(guildId) {
    this.guildCache.invalidate(guildId);
  }

  invalidateUser(userId) {
    this.userCache.invalidate(userId);
  }

  clearCache() {
    this.guildCache.clear();
    this.userCache.clear();
  }

  getStats() {
    return {
      guilds: this.guildCache.getStats(),
      users: this.userCache.getStats()
    };
  }
}

// Export singleton instance
module.exports = new Prefix();
