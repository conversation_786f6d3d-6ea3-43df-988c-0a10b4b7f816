/**
 * Base Cache Class - Common cache behavior for all cache types
 * Provides two-stage caching: Active -> Expired -> Cleanup
 * Automatically handles database operations
 */
class BaseCache {
  constructor(options = {}) {
    this.cache = new Map(); // Main cache storage
    this.ttl = options.ttl || 60 * 60 * 1000; // 1 hour default TTL for active
    this.expiredTtl = options.expiredTtl || 24 * 60 * 60 * 1000; // 24 hours for expired
    this.cleanupInterval = options.cleanupInterval || 30 * 60 * 1000; // 30 min cleanup
    this.expiredCleanupInterval = options.expiredCleanupInterval || 2 * 60 * 60 * 1000; // 2 hours

    // Database configuration
    this.modelName = options.modelName; // e.g., 'user', 'guild', 'blacklist'
    this.keyField = options.keyField; // e.g., 'UserID', 'GuildID', 'ServerID'
    this.fieldsToCache = options.fieldsToCache || []; // e.g., ['LastFMUsername', 'SelfPrefix']

    // Start cleanup intervals
    this.startCleanupIntervals();
  }

  /**
   * Get item from cache with two-stage lookup
   * @param {string} key - Cache key
   * @returns {Object|null} Cached data or null
   */
  get(key) {
    const now = Date.now();
    const cached = this.cache.get(key);
    
    if (!cached) return null;
    
    // Check if active (fresh)
    if (cached.status === 'active' && cached.timestamp + this.ttl > now) {
      return cached;
    }
    
    // Check if expired (fallback)
    if (cached.status === 'expired') {
      return cached;
    }
    
    // Active but expired - move to expired status
    if (cached.status === 'active' && cached.timestamp + this.ttl <= now) {
      cached.status = 'expired';
      return cached;
    }
    
    return null;
  }

  /**
   * Set item in cache as active
   * @param {string} key - Cache key
   * @param {Object} data - Data to cache
   */
  set(key, data) {
    const cacheEntry = {
      ...data,
      status: 'active',
      timestamp: Date.now()
    };
    
    this.cache.set(key, cacheEntry);
  }

  /**
   * Check if cache has active (fresh) data
   * @param {string} key - Cache key
   * @returns {boolean}
   */
  hasActive(key) {
    const cached = this.get(key);
    return cached && cached.status === 'active';
  }

  /**
   * Check if cache has expired (fallback) data
   * @param {string} key - Cache key
   * @returns {boolean}
   */
  hasExpired(key) {
    const cached = this.get(key);
    return cached && cached.status === 'expired';
  }

  /**
   * Invalidate (delete) cache entry
   * @param {string} key - Cache key
   */
  invalidate(key) {
    this.cache.delete(key);
  }

  /**
   * Clear all cache
   */
  clear() {
    this.cache.clear();
  }

  /**
   * Move active entries to expired status
   */
  moveActiveToExpired() {
    const now = Date.now();
    
    for (const [key, data] of this.cache.entries()) {
      if (data.status === 'active' && data.timestamp + this.ttl < now) {
        data.status = 'expired';
      }
    }
  }

  /**
   * Remove expired entries completely
   */
  cleanupExpired() {
    const now = Date.now();
    
    for (const [key, data] of this.cache.entries()) {
      if (data.status === 'expired' && data.timestamp + this.expiredTtl < now) {
        this.cache.delete(key);
      }
    }
  }

  /**
   * Start cleanup intervals
   */
  startCleanupIntervals() {
    // Move active to expired
    setInterval(() => {
      this.moveActiveToExpired();
    }, this.cleanupInterval);
    
    // Remove expired entries
    setInterval(() => {
      this.cleanupExpired();
    }, this.expiredCleanupInterval);
  }

  /**
   * Get cache statistics
   * @returns {Object} Cache stats
   */
  getStats() {
    const entries = Array.from(this.cache.values());
    const active = entries.filter(e => e.status === 'active').length;
    const expired = entries.filter(e => e.status === 'expired').length;
    
    return {
      total: this.cache.size,
      active,
      expired,
      ttl: this.ttl,
      expiredTtl: this.expiredTtl
    };
  }

  /**
   * Get all cache entries (for debugging)
   * @returns {Map} All cache entries
   */
  getAll() {
    return this.cache;
  }

  /**
   * Get field value from cache or database
   * @param {string} key - Cache key (e.g., userId, guildId)
   * @param {string} field - Field name to get
   * @returns {Promise<any>} Field value
   */
  async getField(key, field) {
    const cached = this.get(key);

    // Check if we have active (fresh) cache
    if (cached && this.hasActive(key)) {
      return cached[field];
    }

    // Check if we have expired cache (fallback while fetching new data)
    if (cached && this.hasExpired(key)) {
      // Use expired data as fallback, but fetch fresh data in background
      this.refreshFromDatabase(key); // Non-blocking refresh
      return cached[field];
    }

    // No cache or cache too old - fetch from database
    return await this.fetchFromDatabase(key, field);
  }

  /**
   * Set field value in cache and database
   * @param {string} key - Cache key
   * @param {string} field - Field name to set
   * @param {any} value - Value to set
   * @returns {Promise<boolean>} Success status
   */
  async setField(key, field, value) {
    if (!this.isDatabaseReady()) {
      return false;
    }

    try {
      const Model = this.getModel();

      // Create update object
      const updateData = { [field]: value };
      const keyData = { [this.keyField]: key };

      // Update or create document
      await Model.findOneAndUpdate(
        keyData,
        updateData,
        { upsert: true, new: true }
      );

      // Update cache
      const existingCache = this.get(key) || {};
      const cacheData = {
        ...existingCache,
        [this.keyField]: key,
        [field]: value
      };

      this.set(key, cacheData);
      return true;
    } catch (error) {
      console.error(`Error setting ${field}:`, error);
      return false;
    }
  }

  /**
   * Fetch data from database and cache it
   * @param {string} key - Cache key
   * @param {string} field - Specific field to return (optional)
   * @returns {Promise<any>} Field value or full data
   */
  async fetchFromDatabase(key, field = null) {
    if (!this.isDatabaseReady()) {
      return field ? null : {};
    }

    try {
      const Model = this.getModel();
      const keyData = { [this.keyField]: key };
      const document = await Model.findOne(keyData);

      // Build cache data with only the fields we want to cache
      const cacheData = { [this.keyField]: key };

      if (this.fieldsToCache.length > 0) {
        // Cache specific fields
        this.fieldsToCache.forEach(fieldName => {
          cacheData[fieldName] = document ? document[fieldName] : null;
        });
      } else {
        // Cache all fields if no specific fields specified
        if (document) {
          Object.assign(cacheData, document.toObject());
        }
      }

      this.set(key, cacheData);
      return field ? cacheData[field] : cacheData;
    } catch (error) {
      console.error('Error fetching from database:', error);
      return field ? null : {};
    }
  }

  /**
   * Background refresh for expired data
   * @param {string} key - Cache key
   */
  async refreshFromDatabase(key) {
    try {
      await this.fetchFromDatabase(key);
    } catch (error) {
      console.error('Error refreshing data:', error);
    }
  }

  /**
   * Get database model
   * @returns {Object} Mongoose model
   */
  getModel() {
    if (!this.modelName) {
      throw new Error('Model name not specified in cache configuration');
    }

    try {
      const { User, Guild, Blacklist } = require('../../index');

      switch (this.modelName.toLowerCase()) {
        case 'user':
          return User;
        case 'guild':
          return Guild;
        case 'blacklist':
          return Blacklist;
        default:
          throw new Error(`Unknown model name: ${this.modelName}`);
      }
    } catch (error) {
      console.error(`Error loading model ${this.modelName}:`, error);
      throw error;
    }
  }

  /**
   * Check if database is ready (common utility)
   * @returns {boolean}
   */
  isDatabaseReady() {
    try {
      const { isDatabaseReady } = require('../../index');
      return isDatabaseReady();
    } catch (error) {
      console.error('Error checking database status:', error);
      return false;
    }
  }
}

module.exports = BaseCache;
