const mongoose = require('mongoose');

const autoroleSchema = new mongoose.Schema({
  GuildID: {
    type: String,
    required: true,
    unique: true,
    index: true
  },
  RoleIDs: {
    type: [String],
    default: [],
    validate: {
      validator: function(roles) {
        return roles.length <= 10; // Maximum 10 autoroles
      },
      message: 'Maximum 10 autoroles allowed per guild'
    }
  }
}, {
  timestamps: true
});

// Create index for efficient queries
autoroleSchema.index({ GuildID: 1 });

module.exports = mongoose.model('Autorole', autoroleSchema);
