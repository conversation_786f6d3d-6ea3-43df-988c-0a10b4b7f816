const mongoose = require('mongoose');

const moderationSchema = new mongoose.Schema({
  guildId: {
    type: String,
    required: true,
    index: true
  },
  caseId: {
    type: Number,
    required: true
  },
  userId: {
    type: String,
    required: true,
    index: true
  },
  moderatorId: {
    type: String,
    required: true
  },
  action: {
    type: String,
    required: true,
    enum: ['warn', 'unwarn', 'mute', 'unmute', 'kick', 'ban', 'unban', 'jail', 'unjail', 'role_save']
  },
  reason: {
    type: String,
    required: true
  },
  duration: {
    type: String,
    default: null
  },
  timestamp: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Compound index for efficient queries
moderationSchema.index({ guildId: 1, caseId: 1 }, { unique: true });
moderationSchema.index({ guildId: 1, userId: 1 });
moderationSchema.index({ guildId: 1, action: 1 });

module.exports = mongoose.model('Moderation', moderationSchema);
