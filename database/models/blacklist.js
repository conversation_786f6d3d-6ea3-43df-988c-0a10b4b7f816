const mongoose = require('mongoose');

const blacklistSchema = new mongoose.Schema({
  ServerID: {
    type: String,
    required: true,
    unique: true
  },
  Reason: {
    type: String,
    default: '...'
  },
  BlacklistedBy: {
    type: String,
    required: true
  }
}, {
  timestamps: true
});

// Create indexes for better performance
blacklistSchema.index({ ServerID: 1 });

module.exports = mongoose.model('Blacklist', blacklistSchema);
