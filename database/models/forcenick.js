const mongoose = require('mongoose');

const forcenickSchema = new mongoose.Schema({
  GuildID: {
    type: String,
    required: true,
    index: true
  },
  UserID: {
    type: String,
    required: true
  },
  ForcedNickname: {
    type: String,
    required: true
  },
  SetBy: {
    type: String,
    required: true
  },
  SetAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Create compound index for efficient queries
forcenickSchema.index({ GuildID: 1, UserID: 1 }, { unique: true });

module.exports = mongoose.model('Forcenick', forcenickSchema);
