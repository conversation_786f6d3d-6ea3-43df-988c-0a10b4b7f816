const mongoose = require('mongoose');

const autoreactSchema = new mongoose.Schema({
  GuildID: {
    type: String,
    required: true,
    index: true
  },
  Word: {
    type: String,
    required: true
  },
  EmojiID: {
    type: String,
    default: null
  },
  EmojiName: {
    type: String,
    required: true
  },
  IsCustom: {
    type: Boolean,
    default: false
  }
}, {
  timestamps: true
});

// Create compound index for efficient queries
autoreactSchema.index({ GuildID: 1, Word: 1 });
autoreactSchema.index({ GuildID: 1, EmojiID: 1, EmojiName: 1, IsCustom: 1 });

module.exports = mongoose.model('Autoreact', autoreactSchema);
