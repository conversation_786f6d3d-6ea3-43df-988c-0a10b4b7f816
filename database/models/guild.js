const mongoose = require('mongoose');

const guildSchema = new mongoose.Schema({
  GuildID: {
    type: String,
    required: true,
    unique: true
  },
  Prefix: {
    type: String,
    default: ','
  },

  // Welcome/Leave/Boost Message Configuration
  WelcomeChannel: {
    type: String,
    default: null
  },
  WelcomeMessage: {
    type: String,
    default: null
  },
  LeaveChannel: {
    type: String,
    default: null
  },
  LeaveMessage: {
    type: String,
    default: null
  },
  BoostChannel: {
    type: String,
    default: null
  },
  BoostMessage: {
    type: String,
    default: null
  },

  // Jail System Configuration
  JailRoleID: {
    type: String,
    default: null
  },
  JailChannelID: {
    type: String,
    default: null
  },

  // Vanity System Configuration
  Vanity: {
    type: String,
    default: null
  },
  VanityRoles: {
    type: [String],
    default: []
  },
  VanityMessage: {
    type: String,
    default: null
  },
  VanityLogChannel: {
    type: String,
    default: null
  },

  // Moderation settings will be added here as we enable moderation commands
  // Anti-nuke configuration will be added here as we enable anti-nuke commands
}, {
  timestamps: true
});

// Create indexes for better performance
guildSchema.index({ GuildID: 1 });

module.exports = mongoose.model('Guild', guildSchema);
