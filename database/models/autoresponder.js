const mongoose = require('mongoose');

const autoresponderSchema = new mongoose.Schema({
  GuildID: {
    type: String,
    required: true,
    index: true
  },
  Trigger: {
    type: String,
    required: true
  },
  Response: {
    type: String,
    required: true
  },
  Reply: {
    type: Boolean,
    default: false
  },
  Strict: {
    type: Boolean,
    default: true
  },
  Delete: {
    type: Boolean,
    default: false
  },
  SelfDestruct: {
    type: Number,
    default: 0,
    min: 0,
    max: 300 // Maximum 5 minutes
  }
}, {
  timestamps: true
});

// Create compound index for efficient queries
autoresponderSchema.index({ GuildID: 1, Trigger: 1 }, { unique: true });
autoresponderSchema.index({ GuildID: 1 });

module.exports = mongoose.model('Autoresponder', autoresponderSchema);
