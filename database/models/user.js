const mongoose = require('mongoose');

const userSchema = new mongoose.Schema({
  UserID: {
    type: String,
    required: true,
    unique: true
  },
  SelfPrefix: {
    type: String,
    default: null
  },
  Blacklisted: {
    type: Boolean,
    default: false
  },
  Timezone: {
    type: String,
    default: null
  },
  LastFMUsername: {
    type: String,
    default: null
  },
  // Username history will be added here as we enable username tracking
}, {
  timestamps: true
});

// Create indexes for better performance
userSchema.index({ UserID: 1 });

module.exports = mongoose.model('User', userSchema);
