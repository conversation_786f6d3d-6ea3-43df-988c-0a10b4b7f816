const mongoose = require('mongoose');

const afkSchema = new mongoose.Schema({
  GuildID: {
    type: String,
    required: true,
    index: true
  },
  UserID: {
    type: String,
    required: true
  },
  Message: {
    type: String,
    required: true,
    default: 'AFK'
  },
  TimeAgo: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Create compound index for efficient queries
afkSchema.index({ GuildID: 1, UserID: 1 }, { unique: true });

module.exports = mongoose.model('AFK', afkSchema);
