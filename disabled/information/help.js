const { EmbedBuilder } = require('discord.js')
const { prefix, embedcolor, support, cmdcount } = require('./../../config.json')
const globaldataschema = require('../../database/global')

module.exports = {
  name: "help",
  aliases: ['h'],
  description: `Shows all available bot commands`,
  usage: '{guildprefix}help\n{guildprefix}help [command]',
  run: async(client, message, args) => {

    const globaldata = await globaldataschema.findOne({ GuildID: message.guild.id })

    if (globaldata) {
      var guildprefix = globaldata.Prefix
    } else if (!globaldata) {
      guildprefix = prefix
    }

    const command = client.commands.get(args[0]) || client.commands.get(client.aliases.get(args[0]))
  
    if (command) {

      const embed = new EmbedBuilder()

      .setColor(embedcolor)
      .setTitle(`Command: ${command.name}`)
      .setDescription(`${command.description}`)

      const fields = [];

      if (command.subcommands) {
        let subcommands = command.subcommands
        subcommands = subcommands.replaceAll('{guildprefix}', guildprefix)
        fields.push({ name: '**subcommands**', value: `${subcommands}`, inline: false });
      }

      if (command.aliases) {
        fields.push({ name: '**aliases**', value: `${command.aliases.join(', ')}`, inline: false });
      }


      if (command.usage) {
        let usage = command.usage
        usage = usage.replaceAll('{guildprefix}', guildprefix)
        fields.push({ name: '**Syntax**', value: `\`\`\`${usage}\`\`\``, inline: false });
      }


      if (fields.length > 0) {
        embed.addFields(...fields);
      }

      return message.channel.send({ embeds: [embed] })

    } else if (args.length > 0) {

      const embed = new EmbedBuilder()

      .setColor(embedcolor)
      .setDescription(`This cmd **does not exict**, pls check within the **dev server**, \`;support\` `)

      return message.channel.send({ embeds: [embed] })

    } else {

      return message.channel.send(`pls join the **dev server** for help, \`;support\` `)
    }
  }
}