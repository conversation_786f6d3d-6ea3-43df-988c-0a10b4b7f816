const { EmbedBuilder } = require('discord.js')
const { prefix, embedcolor } = require('./../../config.json')
const globaldataschema = require('../../database/global')

module.exports = {
  name: "report",
  aliases: ['bug', 'issue'],
  description: `Report bugs/issues with the bot`,
  usage: '{guildprefix}report [comment]',
  run: async(client, message, args) => {

    const globaldata = await globaldataschema.findOne({ GuildID: message.guild.id })

    if (globaldata) {
      var guildprefix = globaldata.Prefix
    } else if (!globaldata) {
      guildprefix = prefix
    }
  
    const bugreport = args.join(" ");

    if (!args[0]) {
      return client.commands.get('help').run(client, message, ['report']);
    }

    const bugreportchannel = client.channels.cache.get('1379656294994280570')

    const embed = new EmbedBuilder()

    .setColor(embedcolor)
    .setTitle('New issue reported')
    .setDescription(`> ${bugreport}`)
    .setFooter({ text: `sent by ${message.author.tag}` })

    bugreportchannel.send({ embeds: [embed] })
    message.react('👍')
  }
}