const { Em<PERSON><PERSON><PERSON><PERSON>, ActionRow<PERSON>uilder, <PERSON><PERSON><PERSON><PERSON>er, ButtonStyle } = require('discord.js');
const { embedcolor, invite, support } = require('./../../config.json')

module.exports = {
  name: "invite",
  aliases: ['inv'],
  description: `Send an invite link of the bot`,
  usage: '{guildprefix}invite',
  run: async(client, message, args) => {

    const embed = new EmbedBuilder()

    .setColor(embedcolor)
    .setDescription(`> Contact **[iPhone](https://discord.com/users/1243888419403206660)** if you have any questions..`)

    // Create button
    const row = new ActionRowBuilder()
      .addComponents(
        new ButtonBuilder()
          .setLabel('Invite me')
          .setStyle(ButtonStyle.Link)
          .setURL(invite)
      );

    // Send message with embed and button
    message.channel.send({ embeds: [embed], components: [row] });
  }
}