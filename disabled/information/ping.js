module.exports = {
  name: "latency",
  aliases: ['ms', 'ping'],
  description: `Fetch the bot latency if its acting slow`,
  usage: '{guildprefix}latency',
  run: async (client, message, args) => {

    // Options for random mention target
    const targets = ['no one', 'everyone', 'ur mom', 'horny asian women', 'horny asian man', 'iPhone'];
    const randomTarget = targets[Math.floor(Math.random() * targets.length)];

    message.channel.send("latency...").then(async msg => {
      const ping = Date.now() - message.createdTimestamp;
      setTimeout(() => {
        msg.edit(`it took \`\`18ms\`\` to **ping ${randomTarget}** (edit: \`\`${ping} ms\`\`)`).catch(() => {});
      }, 69);
    });
  }
}