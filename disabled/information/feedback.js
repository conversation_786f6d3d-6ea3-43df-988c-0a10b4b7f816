const { EmbedBuilder } = require('discord.js')
const { prefix, embedcolor } = require('./../../config.json')
const globaldataschema = require('../../database/global')

module.exports = {
  name: "feedback",
  aliases: ['fb', 'review', 'suggest', 'suggestion'],
  description: `Send some a review/suggestion for the dev`,
  usage: '{guildprefix}feedback [comment]',
  run: async(client, message, args) => {

    const globaldata = await globaldataschema.findOne({ GuildID: message.guild.id })

    if (globaldata) {
      var guildprefix = globaldata.Prefix
    } else if (!globaldata) {
      guildprefix = prefix
    }
  
    const feedback = args.join(" ");

    if (!args[0]) {
      return client.commands.get('help').run(client, message, ['feedback']);
    }

    const feedbackch = client.channels.cache.get('1379656294994280570')

    const embed = new EmbedBuilder()

    .setColor(embedcolor)
    .setTitle('New feedback received')
    .setDescription(`> ${feedback}`)
    .setFooter({ text: `sent by ${message.author.tag}` })

    feedbackch.send({ embeds: [embed] })
    message.react('👍')
  }
}   