const { support } = require('./../../config.json');
const { ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');

module.exports = {
  name: "support",
  description: `Get invite from official server`,
  usage: '{guildprefix}support',
  run: async (client, message, args) => {

    const row = new ActionRowBuilder().addComponents(
      new ButtonBuilder()
        .setLabel('Join here')
        .setStyle(ButtonStyle.Link)
        .setURL(support)
    );

    message.channel.send({
      content: `${message.author}, Join this server for any help or questions!`,
      components: [row]
    });
  }
}