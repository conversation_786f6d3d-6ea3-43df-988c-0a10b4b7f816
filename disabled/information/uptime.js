const { EmbedBuilder } = require('discord.js')
const { embedcolor } = require('./../../config.json')

module.exports = {
  name: "uptime",
  description: 'check how long I has been running',
  usage: '{guildprefix}uptime',
  run: async(client, message, args) => {

    let totalseconds = (client.uptime / 1000);
    let days = Math.floor(totalseconds / 86400);
    totalseconds %= 86400;
    let hours = Math.floor(totalseconds / 3600);
    totalseconds %= 3600;
    let minutes = Math.floor(totalseconds / 60);
    let seconds = Math.floor(totalseconds % 60);

    const embed = new EmbedBuilder()

    .setColor(embedcolor)
    .setDescription(`I am running for **${days}d ${hours}h ${minutes}m ${seconds}s** non stop!!`)

    message.channel.send({ embeds: [embed] })
  }
}