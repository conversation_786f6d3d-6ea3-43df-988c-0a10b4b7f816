const { Message, Client, PermissionFlagsBits } = require("discord.js");
const { hasPermission } = require('../../utils/permissions');

module.exports = {
    name: "say",
    description: "Say a message",
    aliases: "{guildprefix}say [message]",

    run: async (client, message, args) => {
        if (!hasPermission(message, PermissionFlagsBits.Administrator, 'Administrator')) return;

        const text = args.join(" ");
        if(!text) return client.commands.get('help').run(client, message, ['say']);

        await message.channel.send(text);
    },
};