const { <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, But<PERSON>B<PERSON>er, ButtonStyle } = require("discord.js");
const { embedcolor } = require('./../../config.json')
const { embeds } = require('../../utils/embeds')

module.exports = {
  name: "firstmessage",
  aliases: ['first'],
  description: `Get a link for the first message in a channel`,
  usage: '{guildprefix}firstmessage [channel]',
  run: async(client, message, args) => {

    let targetChannel = message.channel;

    if (args[0]) {
      const channelResult = message.mentions.channels.first() ?
        { found: true, channel: message.mentions.channels.first() } :
        client.findChannel(message.guild, args[0]);

      if (!channelResult.found) {
        return client.commands.get('help').run(client, message, ['firstmessage']);
      }

      targetChannel = channelResult.channel;
    }

    // Check if the channel is a text-based channel
    if (!targetChannel.isTextBased()) {
      return embeds.warn.send(message, `**${targetChannel.name}** is **not a valid** channel!`);
    }

    const fetchmessages = await targetChannel.messages.fetch({
      after: 1,
      limit: 1,
    });

    const firstmessage = fetchmessages.first();

    if (!firstmessage) {
      return embeds.warn.send(message, `**${targetChannel.name}** has **no messages** to display!`);
    }

    const embed = new EmbedBuilder()
    .setColor(embedcolor)
    .setDescription(`Click below to jump to the **first message by ${firstmessage.author.username}**!`)

    const button = new ButtonBuilder()
    .setLabel('first msg')
    .setStyle(ButtonStyle.Link)
    .setURL(firstmessage.url)

    const row = new ActionRowBuilder()
    .addComponents(button)

    return message.channel.send({ embeds: [embed], components: [row] })
  }
}