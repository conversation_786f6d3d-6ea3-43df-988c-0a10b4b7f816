const fetch = require('node-fetch');

module.exports = {
  name: "yomama",
  aliases: ['ym', 'yomomma'],
  description: `sends a random yo mama joke`,
  usage: '{guildprefix}yomama',
  run: async(client, message, args) => {

    try {
      const res = await fetch('https://www.yomama-jokes.com/api/v1/jokes/random/');

      if (!res.ok) {
        throw new Error(`API returned ${res.status}`);
      }

      const data = await res.json();

      message.channel.send({ content: data.joke })
    } catch (error) {
      message.channel.send(`You're not funny`);
    }
  }
}