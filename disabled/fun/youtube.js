const { ensureGuildData } = require('../../database/global')
const { embeds } = require('../../utils/embedbuilder')
const ytsearch = require("yt-search");

module.exports = {
  name: "youtube",
  aliases: ['yt'],
  description: `search for a song/video on youtube`,
  usage: '{guildprefix}youtube [song/video]',
  run: async(client, message, args) => {

    await ensureGuildData(message.guild.id);

    const youtubesearch = args.join(' ')

    if(!youtubesearch) {
      return client.commands.get('help').run(client, message, ['youtube']);
    }

    try {
      const res = await ytsearch(youtubesearch);

      if (!res || !res.videos || res.videos.length === 0) {
        return embeds.warn.send(message, 'No results found for your search');
      }

      const video = res.videos[0];

      if (!video || !video.url) {
        return embeds.warn.send(message, 'No video found');
      }

      return message.channel.send(`||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​||||​|| _ _ _ _ _ _  ${video.url}`);

    } catch (error) {
      return embeds.error.send(message, 'There\'s some issue with **YouTube API**, Please try again later');
    }
  }
}