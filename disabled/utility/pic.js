const { Message, EmbedBuilder, PermissionFlagsBits, ChannelType } = require('discord.js')
const Discord = require('discord.js');

module.exports = {
	name: 'pic',

	/**
	 * @param {Message} message
	 */

	run: async (client, message, args) => {
		if (!message.member.permissions.has(PermissionFlagsBits.ManageRoles)) return message.reply({ embeds: [{ color: "#efa23a", description: `${warn} ${message.author} please make sure to have the correct permissions` }] });
		if (!message.guild.members.me.permissions.has(PermissionFlagsBits.ManageRoles)) return message.reply({ embeds: [{ color: "#efa23a", description: `${warn} ${message.author} please make sure I have permissions` }] });

        const picEmbed = new EmbedBuilder()

        .setColor("#2f3136")
        .setTitle(`${guildprefix}pic`)
        .setDescription('give user pic perms')
        .addFields(
        { name: '**usage**', value: `${guildprefix}pic {user}`, inline: false },
        { name: '**aliases**', value: 'none', inline: false },
        )
  
        message.channel.send({ embeds: [picEmbed] })

		let user = message.mentions.members.first() || message.guild.members.cache.get(args[0]) || message.member;

		const Member = message.mentions.members.first() || message.guild.members.cache.get(args[0])
		if (Member && Member.id == message.author.id) return message.reply({ embeds: [{ color: "2f3136", description: `${warn} ${message.author}: You cannot pic perms **yourself**` }] })
		if (!Member) return message.reply({ embeds: [{ color: "#efa23a", description: `${warn} ${message.author}: I was unable to find a member with that name` }] })
		const role = message.guild.roles.cache.find(role => role.name.toLowerCase() === 'pic')
		if (!role) {
			try {
				message.reply({ embeds: [{ color: "#efa23a", description: `${warn} ${message.author}: There was no **pic** role found` }] }).then(embedMessage => {
					embedMessage.edit({ embeds: [{ color: "#efa23a", description: `${warn} ${message.author}: trying to create the **pic** role` }] })
				});

				let jailrole = await message.guild.roles.create({
					name: 'pic',
					permissions: []
				});
				message.guild.channels.cache.filter(c => c.type === ChannelType.GuildText).forEach(async (channel, id) => {
					await channel.permissionOverwrites.create(jailrole, {
						AttachFiles: true,
						EmbedLinks: true
					})
				});
				message.guild.channels.cache.filter(c => c.type === ChannelType.GuildVoice).forEach(async (channel, id) => {
					await channel.permissionOverwrites.create(jailrole, {
						ViewChannel: false,
						Connect: false
					})
				});
				message.reply({ embeds: [{ color: "#a3eb7b", description: `${approve} ${message.author}: The **pic** role has been created` }] })
			} catch (error) {

				message.channel.send(error)
			}
		};
		let role2 = message.guild.roles.cache.find(r => r.name.toLowerCase() === 'pic')
		if (role2 && Member.roles.cache.has(role2.id)) return message.reply({ embeds: [{ color: "#efa23a", description: `${warn} ${message.author}: the user **${user.user.tag}** already has pic perms` }] })
		if (role2) await Member.roles.add(role2)
		message.reply({ embeds: [{ color: "#a3eb7b", description: `${approve}: **${user.user.tag}** has been given pic perms` }] })
	}
}