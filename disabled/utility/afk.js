const { EmbedBuilder } = require("discord.js");
const { embedcolor } = require('./../../config.json')
const afkschema = require('../../database/afk')

module.exports = {
  name: "afk",
  description: `Set an afk status for when you are mentioned`,
  usage: '{guildprefix}afk [reason]',
  run: async(client, message, args) => {

    const content = args.join(" ") ? args.join(' ') : "AFK"

    afkschema.findOne({ GuildID: message.guild.id, UserID: message.author.id }, async(err, data) => {
    
      if(data) {

        return;

      } else {

        let afkdata = new afkschema({
          GuildID: message.guild.id,
          UserID: message.author.id,
          Message: content, 
          TimeAgo: Date.now()
        })

        afkdata.save()
        
        const embed = new EmbedBuilder()

        .setColor(embedcolor)
        .setDescription(`<@${message.author.id}>: You're now afk with the status: **${content}**`)
        
        return message.channel.send({ embeds: [embed] })
      }
    })    
  }
}