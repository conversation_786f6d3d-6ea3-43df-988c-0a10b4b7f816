const { EmbedBuilder } = require("discord.js");
const { embedcolor } = require('./../../config.json')
const fetch = require("node-fetch");

module.exports = {
  name: "bitcoin",
  aliases: ['btc'],
  description: "get the current bitcoin price (usd default)",
  usage: '{guildprefix}bitcoin',
  run: async(client, message, args) => {

    try {
      const api_url = (`https://api.coingecko.com/api/v3/simple/price?ids=bitcoin&vs_currencies=usd&include_24hr_vol=true&include_24hr_change=true&include_last_updated_at=true`)

      const response = await fetch(api_url);

      if (!response.ok) {
        throw new Error(`API returned ${response.status}`);
      }

      const data = await response.json();

      if (!data || !data.bitcoin) {
        throw new Error('No bitcoin data received from API');
      }

      const { bitcoin } = data

      const change = parseFloat(bitcoin.usd_24h_change).toFixed(2);
      const changeEmoji = change >= 0 ? '📈' : '📉';
      const changeColor = change >= 0 ? '#00ff00' : '#ff0000';

      const embed = new EmbedBuilder()
        .setColor(embedcolor)
        .setAuthor({ name: '₿ Bitcoin Price', iconURL: 'https://cryptologos.cc/logos/bitcoin-btc-logo.png' })
        .setDescription(`**$${bitcoin.usd.toLocaleString()} USD**`)
        .addFields(
          { name: '24h Change', value: `${changeEmoji} ${change}%`, inline: true },
          { name: '24h Volume', value: `$${bitcoin.usd_24h_vol.toLocaleString()}`, inline: true }
        )
        .setFooter({ text: `Requested by ${message.author.tag} • Last updated`, iconURL: message.author.displayAvatarURL({ dynamic: true }) })
        .setTimestamp()

      return message.channel.send({ embeds: [embed] })
    } catch (error) {

      return message.channel.send('❌ Failed to fetch Bitcoin price. Please try again later.');
    }
  }
}