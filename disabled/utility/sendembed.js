const { PermissionFlagsBits } = require("discord.js");
const { ensureGuildData } = require('../../database/global')
const { MessageProcessor } = require('../../utils/embedbuilder')
const { embeds } = require('../../utils/embeds')

module.exports = {
  name: "sendembed",
  aliases: ['se', 'ce'],
  description: "Create your own embed",
  usage: '{guildprefix}sendembed [embed code]\n{guildprefix}sendembed {title: Hello {user}}$v{description: Welcome to {guild.name}}$v{button: blue && Click Me && 🔵}',
  run: async(client, message, args) => {

    if (!client.permissions.hasPermission(message, PermissionFlagsBits.ManageMessages, "Manage Messages")) {
      return;
    }

    await ensureGuildData(message.guild.id);

    if (!args[0]) {
      return client.commands.get('help').run(client, message, ['sendembed']);
    }

    const input = args.join(' ');

    try {
      let embedInput = input;
      if (!input.trim().startsWith('{embed}')) {
        embedInput = `{embed}$v${input}`;
      }

      const variables = {
        user: message.author,
        guild: message.guild,
        channel: message.channel
      };

      return MessageProcessor.process(message.channel, embedInput, variables);

    } catch (error) {
      return embeds.error.send(message, 'Invalid embed syntax. Please check your format.');
    }
  }
}