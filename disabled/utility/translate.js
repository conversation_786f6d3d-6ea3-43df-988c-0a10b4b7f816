const Discord = require('discord.js')
const { color, prefix, mongooseconnection } = require('./../../config.json')
const translate = require('translate-google')
const globaldataschema = require('../../database/global')

module.exports= {
    name : 'translate',
  aliases: ["translator"],
    run : async(client, message, args) => {

        let guildprefix = prefix;

        // Only try to access database if connection is valid
        if (mongooseconnection && mongooseconnection !== "lol") {
          try {
            const globaldata = await globaldataschema.findOne({ GuildID: message.guild.id })
            if (globaldata && globaldata.Prefix) {
              guildprefix = globaldata.Prefix
            }
          } catch (error) {

            // Continue with default prefix
          }
        }

        if (!args[0]) {
            const embed = new Discord.EmbedBuilder()
            .setColor(color)
            .setTitle(`${guildprefix}translate`)
            .setDescription('translate a message')
            .addFields(
            { name: '**usage**', value: `${guildprefix}translate [word]\n${guildprefix}translate picka`, inline: false },
            { name: '**aliases**', value: `translator`, inline: false },
            )
            return message.channel.send({embeds: [embed]})
        }

        try {
            const res = await translate(args.join(" "), {to : 'en'});
            message.channel.send(res)
        } catch (err) {

            message.channel.send('❌ Translation failed. Please try again later.');
        }
    }
}