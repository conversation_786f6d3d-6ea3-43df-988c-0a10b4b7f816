const { PermissionFlagsBits } = require("discord.js");
const { embeds } = require('../../utils/embeds');
const { hasPermission, checkSelfAction, checkBotAction, checkUserMemberHierarchy, checkBotMemberHierarchy } = require('../../utils/permissions');

module.exports = {
  name: "rename",
  aliases: ['nick'],
  description: `change a member's nickname`,
  usage: '{guildprefix}rename [user]\n{guildprefix}rename [user] [nickname]',
  run: async(client, message, args) => {


    if (!hasPermission(message, PermissionFlagsBits.ManageNicknames, 'Manage Nicknames')) return;

    if (!message.guild.members.me.permissions.has(PermissionFlagsBits.ManageNicknames)) {
      return embeds.botPermissions.send(message, 'Manage Nicknames');
    }

    // Check if user provided input
    if (!args[0]) {
      return client.commands.get('help').run(client, message, ['rename']);
    }

    // Use universal user finder with error handling
    let userResult;
    try {
      userResult = client.findUser(message.guild, args[0]);
    } catch (error) {
      console.error('Error in findUser:', error);
      return embeds.error.send(message, 'An error occurred while searching for the user. Please try again.');
    }

    // Check if userResult is valid and has the expected structure
    if (!userResult || typeof userResult !== 'object') {
      return embeds.error.send(message, 'An error occurred while searching for the user. Please try again.');
    }

    if (!userResult.found) {
      return embeds.error.send(message, userResult.error || 'User not found');
    }

    const user = userResult.user;

    let nickname = args.slice(1).join(" ");

    if (!nickname) {
      nickname = null;
    }

    if (!checkSelfAction(message, user, 'rename')) return;
    if (!checkBotAction(message, user)) return;

    // Hierarchy checks
    if (!checkUserMemberHierarchy(message, user)) return;
    if (!checkBotMemberHierarchy(message, user)) return;

    try {
      await user.setNickname(nickname, `Renamed by ${message.author.tag}`);

      if (nickname) {
        embeds.success.send(message, `**${user.user.tag}** has been renamed to **${nickname}**`);
      } else {
        embeds.success.send(message, `**${user.user.tag}**'s nickname has been resetted`);
      }

    } catch (error) {
      console.error('Rename error:', error);
      embeds.error.send(message, 'An error occurred while trying to rename this user.');
    }
  }
}