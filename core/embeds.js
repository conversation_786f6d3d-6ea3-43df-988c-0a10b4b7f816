const { EmbedBuilder } = require('discord.js');
const { colors, emojis } = require('../config/setup');

// Pre-built embed system with user's preferred format
const embeds = {
    success: (message, text) => {
        const embed = new EmbedBuilder()
            .setColor(colors.success)
            .setDescription(`${emojis.success} <@${message.author.id}>: ${text}`)
        return message.channel.send({ embeds: [embed] })
    },

    deny: (message, text) => {
        const embed = new EmbedBuilder()
            .setColor(colors.error)
            .setDescription(`${emojis.error} <@${message.author.id}>: ${text}`)
        return message.channel.send({ embeds: [embed] })
    },

    warn: (message, text) => {
        const embed = new EmbedBuilder()
            .setColor(colors.warn)
            .setDescription(`${emojis.warn} <@${message.author.id}>: ${text}`)
        return message.channel.send({ embeds: [embed] })
    },

    info: (message, text) => {
        const embed = new EmbedBuilder()
            .setColor(colors.info)
            .setDescription(`<@${message.author.id}>: ${text}`)
        return message.channel.send({ embeds: [embed] })
    }
}

module.exports = { embeds };
