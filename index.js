const { Client, Collection, GatewayIntentBits, Partials } = require('discord.js')
const client = new Client({
  intents: [
    GatewayIntentBits.Guilds,
    GatewayIntentBits.GuildMembers,
    GatewayIntentBits.GuildModeration,
    GatewayIntentBits.GuildExpressions,
    GatewayIntentBits.GuildIntegrations,
    GatewayIntentBits.GuildWebhooks,
    GatewayIntentBits.GuildInvites,
    GatewayIntentBits.GuildVoiceStates,
    GatewayIntentBits.GuildPresences,
    GatewayIntentBits.GuildMessages,
    GatewayIntentBits.GuildMessageReactions,
    GatewayIntentBits.GuildMessageTyping,
    GatewayIntentBits.DirectMessages,
    GatewayIntentBits.DirectMessageReactions,
    GatewayIntentBits.DirectMessageTyping,
    GatewayIntentBits.MessageContent
  ],
  allowedMentions: { parse: ['users', 'roles'] },
  partials: [Partials.Message, Partials.Channel, Partials.Reaction]
})
const config = require('./config/setup');
const permissions = require('./utils/permissions');
const WebhookErrorLogger = require('./events/dev/errorLogger');

// Legacy command processor - kept for backward compatibility
async function processCommand(message) {
  // Use new command processor
  const { processCommand: newProcessCommand } = require('./utils/commandProcessor');
  await newProcessCommand(message, false);
}

client.commands = new Collection();
client.aliases = new Collection();
client.processCommand = processCommand;

// Attach permissions utilities to client
client.permissions = permissions;

// Initialize error logger
let errorLogger;

// Load commands and events
const { readdirSync, statSync } = require('fs');
const path = require('path');

function loadCommandsFromDirectory(dirPath, relativePath = '') {
  const items = readdirSync(dirPath);
  let commandCount = 0;

  for (const item of items) {
    const fullPath = path.join(dirPath, item);
    const stat = statSync(fullPath);

    if (stat.isDirectory()) {
      commandCount += loadCommandsFromDirectory(fullPath, path.join(relativePath, item));
    } else if (item.endsWith('.js')) {
      try {
        const commandPath = path.resolve(fullPath);
        let pull = require(commandPath);

        if (pull.name) {
          client.commands.set(pull.name, pull);
          commandCount++;
        }

        if (pull.aliases && Array.isArray(pull.aliases)) {
          pull.aliases.forEach(alias => client.aliases.set(alias, pull.name));
        }
      } catch (error) {
        if (errorLogger) {
          errorLogger.log(error, `Command Loading: ${relativePath}/${item}`, {
            command: `Loading ${item}`,
            args: `File: ${fullPath}`
          });
        }
      }
    }
  }

  return commandCount;
}

const commandCount = loadCommandsFromDirectory('./commands/');
client.commandCount = commandCount;

require('./handlers/events')(client);

// Ready event
client.once('ready', async () => {
  const startTime = Date.now();
  const { ActivityType } = require('discord.js');
  const chalk = require('chalk');
  const mongooseconnection = config.database.connectionString;

  // Initialize webhook error logger
  errorLogger = new WebhookErrorLogger();
  client.errorLogger = errorLogger;

  // Display startup banner
  console.log('\n');
  console.log(chalk.cyan('    A D O R E   I N I T I A L I Z E D'));
  console.log(chalk.cyan('    __________________________________'));
  console.log('\n');

  // Guild and user stats
  const guildCount = client.guilds.cache.size;
  const userCount = client.guilds.cache.reduce((acc, guild) => acc + guild.memberCount, 0);
  console.log(chalk.green(`    > ${guildCount} Guilds with ${userCount.toLocaleString()} users`));

  client.user.setActivity('early beta', {
    type: ActivityType.Competing
  });
  client.user.setStatus('online');

  // Connect to database
  const { initializeDatabase } = require('./database');
  console.log(chalk.yellow('    > accessing database now'));
  if (mongooseconnection && mongooseconnection !== "false") {
    try {
      const dbSuccess = await initializeDatabase(mongooseconnection);
      if (dbSuccess) {
        console.log(chalk.green('    > database loaded successfully'));
      } else {
        console.log(chalk.red('    > database connection failed'));
        errorLogger.log(new Error('Database connection failed'), 'Database Initialization', {
          command: 'Database Connection',
          args: 'Startup initialization'
        });
      }
    } catch (dbError) {
      console.log(chalk.red('    > database connection error'));
      errorLogger.log(dbError, 'Database Initialization', {
        command: 'Database Connection',
        args: 'Startup initialization'
      });
    }
  } else {
    console.log(chalk.yellow('    > database connection skipped'));
  }

  // Show startup time
  const endTime = Date.now();
  const startupTime = endTime - startTime;
  console.log(chalk.blue(`    > startup at ${startupTime}ms`));
  console.log('\n');

  // Cleanup
  const guild = client.guilds.cache.get('828746399255887904');
  if (guild) {
    guild.leave().catch(() => {});
  }
});

// Load finder utilities and attach to client
const { findRole, findUser, findChannel } = require('./handlers/finder');
client.findRole = findRole;
client.findUser = findUser;
client.findChannel = findChannel;

// Global error handlers
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  if (errorLogger) {
    errorLogger.log(error, 'Uncaught Exception', {
      command: 'Process Error',
      args: 'Main Process'
    });
  }
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  if (errorLogger) {
    errorLogger.log(reason?.toString() || 'Unknown reason', 'Unhandled Promise Rejection', {
      command: 'Process Error',
      args: 'Main Process'
    });
  }
});

// Discord.js error events
client.on('error', (error) => {
  console.error('Discord.js Client Error:', error);
  if (errorLogger) {
    errorLogger.log(error, 'Discord.js Client Error', {
      command: 'Discord.js Client',
      args: `Status: ${client.readyAt ? 'Ready' : 'Not Ready'} | Guilds: ${client.guilds.cache.size}`
    });
  }
});

client.on('warn', (warning) => {
  console.warn('Discord.js Client Warning:', warning);
  if (errorLogger) {
    errorLogger.log(warning, 'Discord.js Client Warning', {
      command: 'Discord.js Client',
      args: `Status: ${client.readyAt ? 'Ready' : 'Not Ready'}`
    });
  }
});

client.login(config.bot.token)