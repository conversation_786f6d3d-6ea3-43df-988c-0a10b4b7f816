const { PermissionFlagsBits } = require('discord.js');
const { embeds } = require('../core/embeds');
const config = require('../config/setup');
const permsConfig = require('../config/perms.json');

const devServerID = config.bot.devServerId;

// Build permission levels from perms.json
const PERMISSION_LEVELS = {};
for (const [level, data] of Object.entries(permsConfig)) {
  PERMISSION_LEVELS[level] = {
    roleId: data.roleId,
    name: data.name.split(' (')[0],
    message: `You need to be a **${data.name.split(' (')[0]}** to use this command!`
  };
}

// Permission level checking
async function hasPermissionLevel(message, level) {
  const userId = message.author.id;

  // Check if it's the dev user or has dev role
  if (level === 'dev') {
    // Check if it's the specific dev user ID
    if (userId === PERMISSION_LEVELS.dev?.userId) {
      return true;
    }

    // Check if user has dev role in dev server
    try {
      if (!devServerID || !PERMISSION_LEVELS.dev?.roleId) {
        return false;
      }

      const devServer = message.client.guilds.cache.get(devServerID);
      if (!devServer) {
        return false;
      }

      const member = await devServer.members.fetch(userId).catch(() => null);
      if (!member) {
        return false;
      }

      return member.roles.cache.has(PERMISSION_LEVELS.dev.roleId);
    } catch (error) {
      return false;
    }
  }

  // Check if it's premium level
  if (level === 'premium') {
    try {
      if (!devServerID || !PERMISSION_LEVELS.premium?.roleId) return false;

      const devServer = message.client.guilds.cache.get(devServerID);
      if (!devServer) return false;

      const member = await devServer.members.fetch(userId).catch(() => null);
      if (!member) return false;

      return member.roles.cache.has(PERMISSION_LEVELS.premium.roleId);
    } catch (error) {
      return false;
    }
  }

  return false;
}

async function checkPermissionLevel(message, level) {
  const hasPermission = await hasPermissionLevel(message, level);

  if (!hasPermission) {
    embeds.warn(message, PERMISSION_LEVELS[level]?.message || 'You do not have permission to use this command!');
    return false;
  }

  return true;
}

// Hierarchy checks
function checkUserHierarchy(message, targetMember, sendError = true) {
  if (targetMember.roles.highest.position >= message.member.roles.highest.position) {
    if (sendError) {
      embeds.warn(message, `**${targetMember.user.username}** is above your role!`);
    }
    return false;
  }
  return true;
}

function checkBotHierarchy(message, target, sendError = true) {
  const targetPosition = target.roles ? target.roles.highest.position : target.position;
  const targetName = target.user ? target.user.username : target.name;

  if (targetPosition >= message.guild.members.me.roles.highest.position) {
    if (sendError) {
      embeds.warn(message, `**${targetName}** is above my role!`);
    }
    return false;
  }
  return true;
}

function checkUserRoleHierarchy(message, role, enabled = true) {
  if (!enabled) return true;

  if (role.position >= message.member.roles.highest.position) {
    embeds.warn(message, `**${role.name}** is above your role!`);
    return false;
  }
  return true;
}

function checkBotRoleHierarchy(message, role, enabled = true) {
  if (!enabled) return true;

  if (role.position >= message.guild.members.me.roles.highest.position) {
    embeds.warn(message, `**${role.name}** is above my role!`);
    return false;
  }
  return true;
}

function checkUserMemberHierarchy(message, member, enabled = true) {
  if (!enabled) return true;
  return checkUserHierarchy(message, member, true);
}

function checkBotMemberHierarchy(message, member, enabled = true) {
  if (!enabled) return true;
  return checkBotHierarchy(message, member, true);
}

// Action checks
function checkSelfAction(message, targetUser, action) {
  if (targetUser.id === message.author.id) {
    embeds.warn(message, `You cannot ${action} yourself!`);
    return false;
  }
  return true;
}

function checkBotAction(message, targetUser) {
  if (targetUser.id === message.guild.members.me.id) {
    embeds.warn(message, 'Leave me alone!');
    return false;
  }
  return true;
}

function checkDiscordActionable(message, member, action) {
  let actionable = false;

  switch (action.toLowerCase()) {
    case 'kick':
      actionable = member.kickable;
      break;
    case 'ban':
      actionable = member.bannable;
      break;
    case 'timeout':
    case 'mute':
      actionable = member.moderatable;
      break;
    case 'manage roles':
    case 'jail':
      actionable = member.manageable;
      break;
    default:
      actionable = true;
  }

  if (!actionable) {
    embeds.warn(message, `I cannot ${action} **${member.user.username}** due to Discord restrictions.`);
    return false;
  }
  return true;
}

// Discord permission checks
function hasDiscordPermission(message, permission, permissionName) {
  if (!message.member.permissions.has(permission)) {
    embeds.warn(message, `You need **${permissionName}** permission to use this command!`);
    return false;
  }
  return true;
}

module.exports = {
  // Permission levels
  hasPermissionLevel,
  checkPermissionLevel,
  PERMISSION_LEVELS,

  // Hierarchy checks
  checkUserHierarchy,
  checkBotHierarchy,
  checkUserRoleHierarchy,
  checkBotRoleHierarchy,
  checkUserMemberHierarchy,
  checkBotMemberHierarchy,

  // Action checks
  checkSelfAction,
  checkBotAction,
  checkDiscordActionable,

  // Discord permissions
  hasDiscordPermission,
  PermissionFlagsBits
};